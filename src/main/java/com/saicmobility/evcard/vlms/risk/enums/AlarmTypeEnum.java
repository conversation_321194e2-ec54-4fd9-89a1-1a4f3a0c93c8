package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 报警类型 1、超出电子围栏 2、GPS信号离线 3、逾期未还车 4、车辆异动 5、年检临期 6、保险临期 7、车辆被扣 8、经营风险 9、车辆失联 10、车辆灭失
 * <AUTHOR>
 * @date 2024-01-16
 */

@AllArgsConstructor
@Getter
public enum AlarmTypeEnum {
    OUT_OF_ELECTRONIC_FENCE(1, "超出电子围栏"),
    GPS_SIGNAL_OFFLINE(2, "GPS信号离线"),
    OVERDUE_NOT_RETURN(3, "逾期未还车"),
    VEHICLE_MOVEMENT(4, "车辆异动"),
    INSPECT_ANNUALLY_ADVENT(5, "年检临期"),
    INSURANCE_ADVENT(6, "保险临期"),
    VEHICLE_IS_BROKEN(7, "车辆被扣"),
    BUSINESS_RISK(8, "经营风险"),
    VEHICLE_LOST(9, "车辆失控"),
    //VEHICLE_MISSED(10, "车辆灭失")
    HIGH_RISK_FENCE(11, "进入高危区域")
    ;

    private final Integer code;
    private final String value;

    public static String getValueByCode(Integer code){
        for (AlarmTypeEnum item : AlarmTypeEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getCodeByValue(String value){
        for (AlarmTypeEnum item : AlarmTypeEnum.values()) {
            if (item.getValue().equals(value.trim())){
                return item.getCode();
            }
        }
        return null;
    }

    public static List<Integer> getCodeList(){
        List<Integer> codeList = new ArrayList<>();
        for (AlarmTypeEnum item : AlarmTypeEnum.values()) {
            codeList.add(item.getCode());
        }
        return codeList;
    }
}
