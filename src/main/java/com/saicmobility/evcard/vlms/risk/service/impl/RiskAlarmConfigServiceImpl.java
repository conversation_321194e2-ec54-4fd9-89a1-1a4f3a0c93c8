package com.saicmobility.evcard.vlms.risk.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.saicmobility.evcard.utils.FileUtils;
import com.saicmobility.evcard.utils.LocalDateUtil;
import com.saicmobility.evcard.utils.PbConvertUtil;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.dto.riskAlarmConfig.RiskAlarmConfigData;
import com.saicmobility.evcard.vlms.risk.enums.AlarmTypeEnum;
import com.saicmobility.evcard.vlms.risk.enums.ExcelTypeEnum;
import com.saicmobility.evcard.vlms.risk.enums.FileSourceEnum;
import com.saicmobility.evcard.vlms.risk.enums.OperateTypeEnum;
import com.saicmobility.evcard.vlms.risk.error.ResultCode;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.job.*;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import com.saicmobility.evcard.vlms.risk.service.ExportFileService;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.service.base.BaseService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.AssetsVehicle;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.GetAssetsVehicleByVinReq;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.GetAssetsVehicleByVinRes;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.VlmsAssetsService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
@Service
@Slf4j
public class RiskAlarmConfigServiceImpl extends BaseService implements RiskAlarmConfigService {

    @Resource
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;

    @Resource
    private ExportFileService exportFileService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Resource
    private AutoUpgradeRiskHandler autoUpgradeRiskHandler;

    @Override
    public QueryRiskAlarmConfigRes queryRiskAlarmConfig(QueryRiskAlarmConfigReq req) {
        QueryRiskAlarmConfigRes.Builder builder = QueryRiskAlarmConfigRes.newBuilder().setTotal(tableRiskAlarmConfigService.selectTotal(req));
        List<RiskAlarmConfigData> riskAlarmConfigs = tableRiskAlarmConfigService.selectList(req);
        if (CollectionUtil.isNotEmpty(riskAlarmConfigs)){
            for (RiskAlarmConfigData riskAlarmConfig : riskAlarmConfigs) {
                builder.addList(PbConvertUtil.generateProtoBuffer(riskAlarmConfig, com.saicmobility.evcard.vlms.vlmsriskservice.api.RiskAlarmConfig.class));
            }
        }
        return builder.build();
    }

    @Override
    public QueryRiskAlarmConfigRes exportRiskAlarmConfig(QueryRiskAlarmConfigReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        long total = tableRiskAlarmConfigService.selectTotal(req);
        if (total == 0){
            throw new ServiceException(ResultCode.Fail, "没有符合条件的风控报警配置不能导出");
        }
        //文件根路径
        String mfsRootPath = Global.instance.getMfsRootPath();
        //文件下载地址
        String projectDownloadUrl = Global.instance.getProjectDownloadUrl();

        String fileName = ExcelTypeEnum.EXCEL_TYPE_3.getValue()+"_"+ LocalDateUtil.format(new Date(), LocalDateUtil.UNSIGNED_DATETIME_PATTERN) +".xlsx";
        String templateFile = "template/"+ExcelTypeEnum.EXCEL_TYPE_3.getTemplate()+".xlsx";

        // 开始导出
        String fileName1 = ExcelTypeEnum.EXCEL_TYPE_3.getValue();
        String exportFilePath = FileUtils.getFilePathSuffix(fileName1,1);
        String fullExportFilePath = mfsRootPath + exportFilePath;
        String downLoadFilePath = projectDownloadUrl + exportFilePath;

        Integer exportFileId = exportFileService.startExport(FileSourceEnum.FILE_SOURCE_17.getSource(), fileName, downLoadFilePath, currentUser.getNickName(),
                currentUser.getUserAccount(), currentUser.getOrgId(), currentUser.getOrgName());
        if (exportFileId == null){
            throw new ServiceException("创建导出文件失败");
        }
        // 异步导出
        taskExecutor.execute(()->{
            // 每页条数
            int pageSize = 5000;
            int totalPageNum = ((int)total + pageSize - 1) / pageSize;
            int pageNum = 1;

            List<RiskAlarmConfigData> riskAlarmConfigDataList = new ArrayList<>();
            do {
                QueryRiskAlarmConfigReq build = req.toBuilder().setPageNum(pageNum).setPageSize(pageSize).build();
                List<RiskAlarmConfigData> riskAlarmConfigs = tableRiskAlarmConfigService.selectList(build);
                if (CollectionUtil.isEmpty(riskAlarmConfigs)){
                    break;
                }
                for (RiskAlarmConfigData riskAlarmConfigData : riskAlarmConfigs) {

                    riskAlarmConfigData.setAlarmTypeDesc(AlarmTypeEnum.getValueByCode(riskAlarmConfigData.getAlarmType()));
                    riskAlarmConfigData.setAlarmStartTimeStr(DateUtil.dateToString(riskAlarmConfigData.getAlarmStartTime(), DateUtil.DATE_TYPE1));
                    riskAlarmConfigData.setPauseDeadlineTimeStr(DateUtil.dateToString(riskAlarmConfigData.getPauseDeadlineTime(), DateUtil.DATE_TYPE1));
                }
                riskAlarmConfigDataList.addAll(riskAlarmConfigs);
                pageNum ++;
            }while (totalPageNum >= pageNum);
            //如果目录不存在，则创建目录
            File file = new File(fullExportFilePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            boolean noResult = ObjectUtil.isNull(riskAlarmConfigDataList);
            try (
                    InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream(templateFile);
                    FileOutputStream fileOutputStream = new FileOutputStream(fullExportFilePath);
                    ExcelWriter excelWriter = EasyExcelFactory.write(fileOutputStream).withTemplate(templateStream).build();

            ) {
                WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                //无数据处理
                if(noResult){
                    excelWriter.fill(new ArrayList<>(), writeSheet);
                }else {
                    excelWriter.fill(riskAlarmConfigDataList, writeSheet);
                }
                fileOutputStream.flush();
                excelWriter.finish();
                //导出成功
                exportFileService.exportSuccess(exportFileId, currentUser.getNickName());
            }catch (Exception e) {
                e.printStackTrace();
                log.error(e.getLocalizedMessage(), e);
                //导出失败
                exportFileService.exportFail(exportFileId, e.getLocalizedMessage(), currentUser.getNickName());
            }
        });

        return QueryRiskAlarmConfigRes.ok();
    }

    @Override
    public UpdateRiskAlarmConfigRes updateRiskAlarmConfig(UpdateRiskAlarmConfigReq req) {
        RiskAlarmConfig riskAlarmConfig = tableRiskAlarmConfigService.selectById(req.getId());
        if (riskAlarmConfig == null){
            throw new ServiceException("车辆风控报警配置不存在");
        }
        riskAlarmConfig.setAlarmStartTime(DateUtil.getDateFromTimeStr(req.getAlarmStartTime()+" 00:00:00", DateUtil.DATE_TYPE1));
        riskAlarmConfig.setPauseDeadlineTime(DateUtil.getDateFromTimeStr(req.getPauseDeadlineTime()+" 23:59:59", DateUtil.DATE_TYPE1));
        tableRiskAlarmConfigService.updateById(riskAlarmConfig);

        // 日志
        String logContent = StrUtil.format( "修改车辆风控报警配置, 报警开始时间：【{}】, 报警升级暂停截止时间:【{}】", req.getAlarmStartTime(), req.getPauseDeadlineTime());
        saveOperateLog(logContent, req.getId()+"", OperateTypeEnum.OPERATE_CONFIG.getCode(), req.getCurrentUser());
        return UpdateRiskAlarmConfigRes.ok();
    }

    @Override
    public void insertRiskAlarmConfig(RiskAlarmConfig riskAlarmConfig) {
        tableRiskAlarmConfigService.insert(riskAlarmConfig);
    }

    @Override
    public Long updateRiskAlarmConfig(String vin, Integer alarmType, Date alarmStartTime, Integer pauseDay, Date pauseDeadlineTime, CurrentUser currentUser) {
        RiskAlarmConfig riskAlarmConfig = new RiskAlarmConfig();
        riskAlarmConfig.setVin(vin);
        riskAlarmConfig.setAlarmType(alarmType);
        RiskAlarmConfig config = tableRiskAlarmConfigService.selectRiskAlarmConfig(riskAlarmConfig);

        riskAlarmConfig.setAlarmStartTime(alarmStartTime);
        riskAlarmConfig.setPauseDay(pauseDay);
        riskAlarmConfig.setPauseDeadlineTime(pauseDeadlineTime);
        riskAlarmConfig.setCreateOperName(currentUser.getNickName());
        riskAlarmConfig.setCreateOperAccount(currentUser.getUserAccount());
        riskAlarmConfig.setUpdateTime(new Date());
        tableRiskAlarmConfigService.updateRiskAlarmConfig(riskAlarmConfig);
        return config.getId();
    }

    @Override
    public AddRiskAlarmConfiRes addRiskAlarmConfig(AddRiskAlarmConfiReq req) {

        if (CollectionUtil.isEmpty(req.getVinListList())){
            throw new ServiceException("未提交车辆");
        }

        GetAssetsVehicleByVinRes assetsVehicleByVinRes = vlmsAssetsService.getAssetsVehicleByVin(GetAssetsVehicleByVinReq.newBuilder().addAllVin(req.getVinListList()).build());
        if (assetsVehicleByVinRes == null || CollectionUtil.isEmpty(assetsVehicleByVinRes.getInfoList())){
            throw new ServiceException("未查询到车辆");

        for (AssetsVehicle vehicle : assetsVehicleByVinRes.getInfoList()) {
            Date now = new Date();
            if (AlarmTypeEnum.getCodeList().contains(req.getAlarmType())) {
                RiskAlarmConfig riskAlarmConfig = tableRiskAlarmConfigService.selectRiskAlarmConfig(vehicle.getVin(), req.getAlarmType());
                if (riskAlarmConfig == null) {
                    RiskAlarmConfig alarmConfig = buidRiskAlarmConfig(vehicle, req.getAlarmType(), now);
                    insertRiskAlarmConfig(alarmConfig);
                    saveOperateLog(StrUtil.format("新增车辆风控报警配置,  报警类型:【{}】", AlarmTypeEnum.getValueByCode(req.getAlarmType())), alarmConfig.getId() + "", OperateTypeEnum.OPERATE_CONFIG.getCode(),
                            CurrentUser.newBuilder().setNickName("系统").setUserAccount("System").build());
                    log.info("新增车辆风控报警配置，车架号：【{}】，报警类型:【{}】", vehicle.getVin(), AlarmTypeEnum.getValueByCode(req.getAlarmType()));
                }
            } else {
                for (AlarmTypeEnum alarmTypeEnum : AlarmTypeEnum.values()) {
                    RiskAlarmConfig riskAlarmConfig = tableRiskAlarmConfigService.selectRiskAlarmConfig(vehicle.getVin(), alarmTypeEnum.getCode());
                    if (riskAlarmConfig == null) {
                        RiskAlarmConfig alarmConfig = buidRiskAlarmConfig(vehicle, alarmTypeEnum.getCode(), now);
                        insertRiskAlarmConfig(alarmConfig);
                        saveOperateLog(StrUtil.format("新增车辆风控报警配置,  报警类型:【{}】", alarmTypeEnum.getValue()), alarmConfig.getId() + "", OperateTypeEnum.OPERATE_CONFIG.getCode(),
                                CurrentUser.newBuilder().setNickName("系统").setUserAccount("System").build());
                        log.info("新增车辆风控报警配置，车架号：【{}】，报警类型:【{}】", vehicle.getVin(), AlarmTypeEnum.getValueByCode(req.getAlarmType()));
                    }
                }
            }
        }
        return AddRiskAlarmConfiRes.ok();
    }

    private RiskAlarmConfig buidRiskAlarmConfig(AssetsVehicle assetsVehicle, Integer alarmType, Date now) {
        RiskAlarmConfig riskAlarmConfig = new RiskAlarmConfig();
        riskAlarmConfig.setVin(assetsVehicle.getVin());
     /*   riskAlarmConfig.setPlateNo(assetsVehicle.getPlateNo());
        riskAlarmConfig.setOrgId(assetsVehicle.getPropertyOrgId());
        riskAlarmConfig.setOrgName(assetsVehicle.getPropertyOrgName());
        riskAlarmConfig.setOperateOrgId(assetsVehicle.getOperationOrgId());
        riskAlarmConfig.setOperateOrgName(assetsVehicle.getOperationOrgName());*/
        riskAlarmConfig.setAlarmType(alarmType);
        riskAlarmConfig.setAlarmStartTime(now);
        riskAlarmConfig.setCreateOperName("定时任务");
        return riskAlarmConfig;
    }

    @Resource
    private VehicleElectronicFenceWarningHandler vehicleElectronicFenceWarningHandler;

    @Resource
    private AutoRecoveryRiskHandler autoRecoveryRiskHandler;

    @Resource
    private AutoCancelCheckHandler autoCancelCheckHandler;

    @Resource
    private VehicleGPSOfflineWarningHandler vehicleGPSOfflineWarningHandler;
    @Override
    public WangNengRiskAlarmRes wangNengRiskAlarm(WangNengRiskAlarmReq req) {
        try {
            vehicleElectronicFenceWarningHandler.execute("WD000000000000079");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return WangNengRiskAlarmRes.ok();
    }

    @Override
    public SyncRiskOrderRes syncRiskOrder(SyncRiskOrderReq syncRiskOrderReq) {
        String vin = syncRiskOrderReq.getVin();
        // 1、逾期未还车 2、非固资运营阶段（年检和较强） 3、固资运营阶段（年检和交强险） 4、车辆异动 5、年检临期 6、保险临期
        int type = syncRiskOrderReq.getType();
        switch (type){
            case 1 :
                autoUpgradeRiskHandler.doCarNotReturn(new Date(), vin);
                break;
            case 2 :
                autoUpgradeRiskHandler.doNotOperateVehicle(new Date(), vin);
                break;
            case 3 :
                autoUpgradeRiskHandler.doOperateVehicle(new Date(), vin);
                break;
        }
        //autoUpgradeRiskHandler.doLongRentAlarm(new Date(), vin);

        //autoUpgradeRiskHandler.doOperateVehicle(new Date(), vin);

        //autoUpgradeRiskHandler.doNotOperateVehicle(new Date(), vin);

        //autoUpgradeRiskHandler.doCarNotReturn(new Date(), vin);
        return SyncRiskOrderRes.ok();
    }
}
