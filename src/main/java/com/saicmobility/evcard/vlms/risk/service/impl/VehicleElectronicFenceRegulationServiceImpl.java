package com.saicmobility.evcard.vlms.risk.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.saicmobility.evcard.md.mddataproxy.api.MdDataProxy;
import com.saicmobility.evcard.md.mddataproxy.api.initElectronicFenceRegulationReq;
import com.saicmobility.evcard.utils.FileUtils;
import com.saicmobility.evcard.utils.LocalDateUtil;
import com.saicmobility.evcard.utils.PbConvertUtil;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.bo.ElectronicFenceRegulationBO;
import com.saicmobility.evcard.vlms.risk.dto.electronicFenceRegulation.ElectronicFenceConfigReq;
import com.saicmobility.evcard.vlms.risk.dto.electronicFenceRegulation.ElectronicFenceRegulationLogRes;
import com.saicmobility.evcard.vlms.risk.dto.electronicFenceRegulation.QueryAllProvinceDTORes;
import com.saicmobility.evcard.vlms.risk.dto.electronicFenceRegulation.QueryElectronicFenceConfigDTORes;
import com.saicmobility.evcard.vlms.risk.enums.ExcelTypeEnum;
import com.saicmobility.evcard.vlms.risk.enums.FileSourceEnum;
import com.saicmobility.evcard.vlms.risk.enums.SubProductLineEnum;
import com.saicmobility.evcard.vlms.risk.error.ResultCode;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.excel.ExportErrorInfo;
import com.saicmobility.evcard.vlms.risk.excel.ImportVinDemo;
import com.saicmobility.evcard.vlms.risk.service.ExportFileService;
import com.saicmobility.evcard.vlms.risk.service.VehicleElectronicFenceRegulationService;
import com.saicmobility.evcard.vlms.risk.service.base.BaseService;
import com.saicmobility.evcard.vlms.risk.utils.BaseUtils;
import com.saicmobility.evcard.vlms.risk.utils.ExcelUtil;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.AssetsVehicle;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-23
 */
@Service
@Slf4j
public class VehicleElectronicFenceRegulationServiceImpl extends BaseService implements VehicleElectronicFenceRegulationService {

    @Resource
    private MdDataProxy mdDataProxy;

    @Resource
    private ExportFileService exportFileService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Override
    public QueryElectronicFenceConfigRes queryElectronicFenceConfig(QueryElectronicFenceConfigReq queryElectronicFenceConfigReq) {

        com.saicmobility.evcard.md.mddataproxy.api.QueryElectronicFenceConfigRes queryElectronicFenceConfigRes = mdDataProxy.queryElectronicFenceConfig(com.saicmobility.evcard.md.mddataproxy.api.QueryElectronicFenceConfigReq.newBuilder()
                .setVehicleNo(queryElectronicFenceConfigReq.getVehicleNo())
                .setVin(queryElectronicFenceConfigReq.getVin())
                .setOrgId(queryElectronicFenceConfigReq.getOrgId())
                .setOperationOrgId(queryElectronicFenceConfigReq.getOperationOrgId())
                .setRegulationType(queryElectronicFenceConfigReq.getRegulationType())
                .setProductLine(queryElectronicFenceConfigReq.getProductLine())
                .setBusinessLine(queryElectronicFenceConfigReq.getBusinessLine())
                .setUserOrgId(queryElectronicFenceConfigReq.getCurrentUser().getOrgId().equals("00")?"":queryElectronicFenceConfigReq.getCurrentUser().getOrgId())
                .setPageNum(queryElectronicFenceConfigReq.getPageNum())
                .setPageSize(queryElectronicFenceConfigReq.getPageSize()).build());
        if (queryElectronicFenceConfigRes == null || queryElectronicFenceConfigRes.getListList().isEmpty() || queryElectronicFenceConfigRes.getListCount() <= 0) {
            return QueryElectronicFenceConfigRes.ok();
        }
        QueryElectronicFenceConfigDTORes queryElectronicFenceConfigDTORes = new QueryElectronicFenceConfigDTORes().toRes(queryElectronicFenceConfigRes);
        return PbConvertUtil.generateProtoBuffer(queryElectronicFenceConfigDTORes, QueryElectronicFenceConfigRes.class);
    }



    @Override
    public QueryElectronicFenceDefaultRegulationLogRes queryElectronicFenceDefaultRegulationLog(QueryElectronicFenceDefaultRegulationLogReq queryElectronicFenceDefaultRegulationLogReq) {
        com.saicmobility.evcard.md.mddataproxy.api.QueryElectronicFenceDefaultRegulationLogRes queryElectronicFenceDefaultRegulationLogRes = mdDataProxy.queryElectronicFenceDefaultRegulationLog(com.saicmobility.evcard.md.mddataproxy.api.QueryElectronicFenceDefaultRegulationLogReq.newBuilder()
                .setElectronicFenceRegulationId(queryElectronicFenceDefaultRegulationLogReq.getElectronicFenceRegulationId())
                .setPageNum(queryElectronicFenceDefaultRegulationLogReq.getPageNum())
                .setPageSize(queryElectronicFenceDefaultRegulationLogReq.getPageSize()).build());
        if (queryElectronicFenceDefaultRegulationLogRes == null || queryElectronicFenceDefaultRegulationLogRes.getListList().isEmpty() || queryElectronicFenceDefaultRegulationLogRes.getListCount() <= 0) {
            return QueryElectronicFenceDefaultRegulationLogRes.ok();
        }
        ElectronicFenceRegulationLogRes electronicFenceRegulationLogRes = new ElectronicFenceRegulationLogRes().toRes(queryElectronicFenceDefaultRegulationLogRes);
        return PbConvertUtil.generateProtoBuffer(electronicFenceRegulationLogRes, QueryElectronicFenceDefaultRegulationLogRes.class);
    }

    @Override
    public DeleteElectronicFenceConfigRes deleteElectronicFenceConfig(DeleteElectronicFenceConfigReq deleteElectronicFenceConfigReq) {
        ElectronicFenceConfigReq deleteReq = new ElectronicFenceConfigReq();
        deleteReq.setId(deleteElectronicFenceConfigReq.getId());
        deleteReq.setCurrentUser(BaseUtils.getCurrentUser(deleteElectronicFenceConfigReq.getCurrentUser()));
        com.saicmobility.evcard.md.mddataproxy.api.DeleteElectronicFenceConfigReq req = PbConvertUtil.generateProtoBuffer(deleteReq, com.saicmobility.evcard.md.mddataproxy.api.DeleteElectronicFenceConfigReq.class);
        com.saicmobility.evcard.md.mddataproxy.api.DeleteElectronicFenceConfigRes deleteElectronicFenceConfigRes = mdDataProxy.deleteElectronicFenceConfig(req);
        if(deleteElectronicFenceConfigRes.getRetCode() !=0 ){
            return DeleteElectronicFenceConfigRes.failed(deleteElectronicFenceConfigRes.getRetCode());
        }
        return DeleteElectronicFenceConfigRes.ok();
    }

    @Override
    public UpdateElectronicFenceConfigRes updateElectronicFenceConfig(UpdateElectronicFenceConfigReq updateElectronicFenceConfigReq) {
        ElectronicFenceConfigReq updateReq = new ElectronicFenceConfigReq();
        updateReq.setId(updateElectronicFenceConfigReq.getId());
        updateReq.setVin(updateElectronicFenceConfigReq.getVin());
        updateReq.setVehicleNo(updateElectronicFenceConfigReq.getVehicleNo());
        updateReq.setRegulationType(updateElectronicFenceConfigReq.getRegulationType());
        updateReq.setPenCityScope(updateElectronicFenceConfigReq.getPenCityScope());
        updateReq.setPenCityIdScope(updateElectronicFenceConfigReq.getPenCityIdScope());
        updateReq.setProductLine(updateElectronicFenceConfigReq.getProductLine());
        updateReq.setCurrentUser(BaseUtils.getCurrentUser(updateElectronicFenceConfigReq.getCurrentUser()));
        com.saicmobility.evcard.md.mddataproxy.api.UpdateElectronicFenceConfigReq req = PbConvertUtil.generateProtoBuffer(updateReq, com.saicmobility.evcard.md.mddataproxy.api.UpdateElectronicFenceConfigReq.class);
        com.saicmobility.evcard.md.mddataproxy.api.UpdateElectronicFenceConfigRes updateElectronicFenceConfigRes = mdDataProxy.updateElectronicFenceConfig(req);
        if(updateElectronicFenceConfigRes.getRetCode() != 0){
            return UpdateElectronicFenceConfigRes.failed(updateElectronicFenceConfigRes.getRetCode());
        }
        return UpdateElectronicFenceConfigRes.ok();
    }

    @Override
    public AddElectronicFenceConfigListRes addElectronicFenceConfigList(AddElectronicFenceConfigListReq addElectronicFenceConfigListReq) {
        ElectronicFenceConfigReq addReq = new ElectronicFenceConfigReq();
        addReq.setRegulationType(addElectronicFenceConfigListReq.getRegulationType());
        if (addElectronicFenceConfigListReq.getRegulationType() == 2 || addElectronicFenceConfigListReq.getRegulationType() == 4) {
            addReq.setVin(addElectronicFenceConfigListReq.getVin());
            addReq.setVehicleNo(addElectronicFenceConfigListReq.getVehicleNo());
        }
        addReq.setPenCityScope(addElectronicFenceConfigListReq.getPenCityScope());
        addReq.setPenCityIdScope(addElectronicFenceConfigListReq.getPenCityIdScope());
        addReq.setProductLine(addElectronicFenceConfigListReq.getProductLine());
        addReq.setBusinessLine(addElectronicFenceConfigListReq.getBusinessLine());
        addReq.setOperationOrgId(addElectronicFenceConfigListReq.getOperationOrgId());
        addReq.setCurrentUser(BaseUtils.getCurrentUser(addElectronicFenceConfigListReq.getCurrentUser()));
        com.saicmobility.evcard.md.mddataproxy.api.AddElectronicFenceConfigListReq req = PbConvertUtil.generateProtoBuffer(addReq, com.saicmobility.evcard.md.mddataproxy.api.AddElectronicFenceConfigListReq.class);
        com.saicmobility.evcard.md.mddataproxy.api.AddElectronicFenceConfigListRes addElectronicFenceConfigListRes = mdDataProxy.addElectronicFenceConfigList(req);
        if(addElectronicFenceConfigListRes.getRetCode() != 0){
            return AddElectronicFenceConfigListRes.failed(addElectronicFenceConfigListRes.getRetCode(),addElectronicFenceConfigListRes.getRetMsg());
        }
        return AddElectronicFenceConfigListRes.ok();
    }

    @Override
    public InsertOrUpdateBicycleRegulationRes insertOrUpdateBicycleRegulation(InsertOrUpdateBicycleRegulationReq req) {
        //1-新增/修改 2-删除
        int operateType = req.getOperateType();
        // 解析excel
        List<ImportVinDemo> vinList = ExcelUtil.read(req.getFilePath(), 0, ImportVinDemo.class);
        if (CollectionUtil.isEmpty(vinList)) {
            throw new ServiceException("请上传车架号");
        }
        if (vinList.size() > 5000){
            throw new ServiceException("单次上传限制5000条");
        }
        List<ExportErrorInfo> errorInfoList = new ArrayList<>();
        int row = 1;
        for (ImportVinDemo vinDemo : vinList) {
            String vin = vinDemo.getVin();
            // 车架号
            if (StringUtil.isBlank(vin)) {
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行车架号为空", row)));
                continue;
            }

            // 判断车辆是否存在
            AssetsVehicle assetsVehicle = getVehicleInfoByVin(vin);
            if (assetsVehicle == null){
                errorInfoList.add(createErrorInfo(vin, "车辆不存在"));
                continue;
            }


            QueryElectronicFenceConfigRes queryElectronicFenceConfigRes = queryElectronicFenceConfig(QueryElectronicFenceConfigReq.newBuilder()
                    .setVin(vin)
                    .setRegulationType(req.getRegulationType())
                    .setPageSize(1)
                    .setPageNum(1)
                    .build());
            if (operateType == 1) {
                if (queryElectronicFenceConfigRes.getRetCode() == 0 && queryElectronicFenceConfigRes.getListCount() > 0) {
                    UpdateElectronicFenceConfigRes updateElectronicFenceConfigRes = updateElectronicFenceConfig(UpdateElectronicFenceConfigReq.newBuilder().setCurrentUser(req.getCurrentUser())
                            .setId(queryElectronicFenceConfigRes.getListList().get(0).getId())
                            .setPenCityIdScope(req.getPenCityIdScope())
                            .setPenCityScope(req.getPenCityScope())
                            .build());
                    if (updateElectronicFenceConfigRes.getRetCode() != 0) {
                        log.error("电子围栏修改失败! vin:" + vin + updateElectronicFenceConfigRes.getRetMsg());
                        errorInfoList.add(createErrorInfo(vin, "电子围栏修改失败，请检查！"));
                    }
                } else if (queryElectronicFenceConfigRes.getRetCode() == 0 && queryElectronicFenceConfigRes.getListCount() == 0) {
                    AddElectronicFenceConfigListRes addElectronicFenceConfigListRes = addElectronicFenceConfigList(AddElectronicFenceConfigListReq.newBuilder().setCurrentUser(req.getCurrentUser())
                            .setVin(vin)
                            .setRegulationType(req.getRegulationType())
                            .setPenCityScope(req.getPenCityScope())
                            .setVehicleNo(assetsVehicle.getPlateNo())
                            .setPenCityIdScope(req.getPenCityIdScope())
                            .setProductLine(assetsVehicle.getProductLine())
                            .setOperationOrgId(assetsVehicle.getOperationOrgId())
                            .build());
                    if (addElectronicFenceConfigListRes.getRetCode() != 0) {
                        log.error("电子围栏新增失败! vin:" + vin + addElectronicFenceConfigListRes.getRetMsg());
                        errorInfoList.add(createErrorInfo(vin, "电子围栏新增失败，请检查！"));
                    }
                }
            } else if (operateType == 2) {
                if (queryElectronicFenceConfigRes.getRetCode() == 0 && queryElectronicFenceConfigRes.getListCount() > 0) {
                    DeleteElectronicFenceConfigRes deleteElectronicFenceConfigRes = deleteElectronicFenceConfig(DeleteElectronicFenceConfigReq.newBuilder().setCurrentUser(req.getCurrentUser()).setId(queryElectronicFenceConfigRes.getListList().get(0).getId()).build());
                    if (deleteElectronicFenceConfigRes.getRetCode() != 0) {
                        log.error("电子围栏删除失败! vin:" + vin + deleteElectronicFenceConfigRes.getRetMsg());
                        errorInfoList.add(createErrorInfo(vin, "电子围栏删除失败，请检查！"));
                    }
                }
            }
            row ++;
        }
        if (CollectionUtil.isNotEmpty(errorInfoList)){
            String fileName = CharSequenceUtil.format("操作电子围栏错误_{}.xlsx", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
            asynchronousUpload(fileName,116,errorInfoList, req.getCurrentUser());
            throw new ServiceException("导入错误：请去文件导出页面，下载导入错误信息");
        }
        return InsertOrUpdateBicycleRegulationRes.ok();
    }

    @Override
    public QueryElectronicFenceConfigRes exportElectronicFenceConfig(QueryElectronicFenceConfigReq req) {
        // 查询 总条数
        QueryElectronicFenceConfigReq totalBuild = req.toBuilder().setPageNum(1).setPageSize(1).build();
        QueryElectronicFenceConfigRes res = queryElectronicFenceConfig(totalBuild);
        if (res.getTotal() == 0){
            throw new ServiceException(ResultCode.Fail, "没有符合条件的电子围栏不能导出");
        }
        CurrentUser currentUser = req.getCurrentUser();
        //文件根路径
        String mfsRootPath = Global.instance.getMfsRootPath();
        //文件下载地址
        String projectDownloadUrl = Global.instance.getProjectDownloadUrl();
        String fileName;
        String templateFile;
        String fileName1;
        if(req.getRegulationType() == 2){
            fileName = ExcelTypeEnum.EXCEL_TYPE_5.getTemplate()+"_"+ LocalDateUtil.format(new Date(), LocalDateUtil.UNSIGNED_DATETIME_PATTERN) +".xlsx";
            templateFile = "template/"+ExcelTypeEnum.EXCEL_TYPE_5.getTemplate()+".xlsx";
            fileName1 = ExcelTypeEnum.EXCEL_TYPE_5.getTemplate();
        }else {
            fileName = ExcelTypeEnum.EXCEL_TYPE_6.getTemplate()+"_"+ LocalDateUtil.format(new Date(), LocalDateUtil.UNSIGNED_DATETIME_PATTERN) +".xlsx";
            templateFile = "template/"+ExcelTypeEnum.EXCEL_TYPE_6.getTemplate()+".xlsx";
            fileName1 = ExcelTypeEnum.EXCEL_TYPE_6.getTemplate();
        }
        // 开始导出
        String exportFilePath = FileUtils.getFilePathSuffix(fileName1,1);
        String fullExportFilePath = mfsRootPath + exportFilePath;
        String downLoadFilePath = projectDownloadUrl + exportFilePath;

        Integer exportFileId = exportFileService.startExport(FileSourceEnum.FILE_SOURCE_18.getSource(), fileName, downLoadFilePath, currentUser.getNickName(),
                currentUser.getUserAccount(), currentUser.getOrgId(), currentUser.getOrgName());
        if (exportFileId == null){
            throw new ServiceException("创建导出文件失败");
        }

        List<ElectronicFenceRegulationBO> electronicFenceRegulationBOS = new ArrayList<>();
        // 异步导出
        taskExecutor.execute(()->{
            // 每页条数
            int pageSize = 5000;
            // long 转 int
          /*  int total = (int)res.getTotal();
            int totalPageNum = (total + pageSize - 1) / pageSize;*/
            int pageNum = 1;
            int maxPage = 100; // 设置最大页数防止无限循环
            do {
                QueryElectronicFenceConfigReq build = req.toBuilder().setPageNum(pageNum).setPageSize(pageSize).build();
                QueryElectronicFenceConfigRes result = queryElectronicFenceConfig(build);
                List<ElectronicFenceRegulationInfo> list = result.getListList();
                if (CollectionUtil.isEmpty(list)){
                    break;
                }
                for (ElectronicFenceRegulationInfo info : list) {
                    ElectronicFenceRegulationBO electronicFenceRegulation = new ElectronicFenceRegulationBO();
                    electronicFenceRegulation.setId(info.getId());
                    if(info.getRegulationType() == 2){
                        electronicFenceRegulation.setVehicleNo(info.getVehicleNo());
                        electronicFenceRegulation.setVin(info.getVin());
                    }
                    /**
                     * 产品线 1:车管中心 2:长租 3:短租 4:公务用车
                     */
                    if(info.getProductLine() == 1){
                        electronicFenceRegulation.setProductLineStr("车管中心");
                    }else if(info.getProductLine() == 2){
                        electronicFenceRegulation.setProductLineStr("长租");
                    }else if(info.getProductLine() == 3){
                        electronicFenceRegulation.setProductLineStr("短租");
                    }else if(info.getProductLine() == 4){
                        electronicFenceRegulation.setProductLineStr("公务用车");
                    }
                    electronicFenceRegulation.setBusinessLineStr(SubProductLineEnum.getValueByCode(info.getBusinessLine()));
                    electronicFenceRegulation.setOrgName(info.getOrgName());
                    electronicFenceRegulation.setOperationOrgName(info.getOperationOrgName());
                    electronicFenceRegulation.setPenCityScope(info.getPenCityScope());
                    electronicFenceRegulationBOS.add(electronicFenceRegulation);
                }
                pageNum ++;

                if(result.getListCount() < pageSize || pageNum > maxPage){
                    break;
                }
            }while (true);

            //如果目录不存在，则创建目录
            File file = new File(fullExportFilePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            boolean noResult = ObjectUtil.isNull(electronicFenceRegulationBOS);
            try (
                    InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream(templateFile);
                    FileOutputStream fileOutputStream = new FileOutputStream(fullExportFilePath);
                    ExcelWriter excelWriter = EasyExcelFactory.write(fileOutputStream).withTemplate(templateStream).build();

            ) {
                WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                //无数据处理
                if(noResult){
                    excelWriter.fill(new ArrayList<>(), writeSheet);
                }else {
                    excelWriter.fill(electronicFenceRegulationBOS, writeSheet);
                }
                fileOutputStream.flush();
                excelWriter.finish();
                //导出成功
                exportFileService.exportSuccess(exportFileId, currentUser.getNickName());
            }catch (Exception e) {
                e.printStackTrace();
                log.error(e.getLocalizedMessage(), e);
                //导出失败
                exportFileService.exportFail(exportFileId, e.getLocalizedMessage(), currentUser.getNickName());
            }
        });
        return QueryElectronicFenceConfigRes.ok();
    }

    @Override
    public InitElectronicFenceConfigRes initElectronicFenceConfig(InitElectronicFenceConfigReq initElectronicFenceConfigReq) {
        mdDataProxy.initElectronicFenceRegulation(initElectronicFenceRegulationReq.newBuilder().build());
        return InitElectronicFenceConfigRes.ok();
    }

    @Override
    public QueryAllCityByProvinceRes queryAllCityByProvince(QueryAllCityByProvinceReq queryAllCityByProvinceReq) {
        com.saicmobility.evcard.md.mddataproxy.api.QueryAllCityByProvinceRes queryAllCityByProvinceRes = mdDataProxy.queryAllCityByProvince(com.saicmobility.evcard.md.mddataproxy.api.QueryAllCityByProvinceReq.newBuilder().setProvinceId(queryAllCityByProvinceReq.getProvinceId()).build());
        if (queryAllCityByProvinceRes == null || queryAllCityByProvinceRes.getCityInfosList().isEmpty() || queryAllCityByProvinceRes.getCityInfosCount() <= 0) {
            return QueryAllCityByProvinceRes.ok();
        }
        com.saicmobility.evcard.vlms.risk.dto.electronicFenceRegulation.QueryAllCityByProvinceRes cityByProvinceRes = new com.saicmobility.evcard.vlms.risk.dto.electronicFenceRegulation.QueryAllCityByProvinceRes();
        List<com.saicmobility.evcard.vlms.risk.dto.CityInfo> cityInfos = new ArrayList<>();
        for(com.saicmobility.evcard.md.mddataproxy.api.CityInfo cityInfo : queryAllCityByProvinceRes.getCityInfosList()){
            com.saicmobility.evcard.vlms.risk.dto.CityInfo info = new com.saicmobility.evcard.vlms.risk.dto.CityInfo();
            info.setCity(cityInfo.getCityName());
            info.setCityid(cityInfo.getCityId());
            info.setStatus(cityInfo.getStatus());
            cityInfos.add(info);
        }
        cityByProvinceRes.setCityInfos(cityInfos);
        return PbConvertUtil.generateProtoBuffer(cityByProvinceRes, QueryAllCityByProvinceRes.class);
    }

    @Override
    public QueryAllProvinceRes queryAllProvince(QueryAllProvinceReq queryAllProvinceReq) {
        com.saicmobility.evcard.md.mddataproxy.api.QueryAllProvinceRes queryAllProvinceRes = mdDataProxy.queryAllProvince(com.saicmobility.evcard.md.mddataproxy.api.QueryAllProvinceReq.newBuilder().build());
        if (queryAllProvinceRes == null || queryAllProvinceRes.getProvinceInfosList().isEmpty() || queryAllProvinceRes.getProvinceInfosCount() <= 0) {
            return QueryAllProvinceRes.ok();
        }
        QueryAllProvinceDTORes res = QueryAllProvinceDTORes.toRes(queryAllProvinceRes.getProvinceInfosList());
        return PbConvertUtil.generateProtoBuffer(res, QueryAllProvinceRes.class);
    }
}
