package com.saicmobility.evcard.vlms.risk.listener;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.OrderRenewal;
import com.extracme.evcard.mq.bean.event.ReturnVehicle;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.enums.AlarmStatusEnum;
import com.saicmobility.evcard.vlms.risk.enums.AlarmTypeEnum;
import com.saicmobility.evcard.vlms.risk.enums.OperateTypeEnum;
import com.saicmobility.evcard.vlms.risk.enums.RecoverMethodEnum;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.service.RiskCommonService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 门店订单还车、续租事件消费监听 改成定时任务处理
 * <AUTHOR>
 * @date 2024-02-24
 */
@Slf4j
@Service
@Deprecated
public class CompleteOrderlistener implements MessageListener {

    @Resource
    private RiskCommonService commonService;

    @Resource
    private SubAssetsService subAssetsService;

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    private RiskAlarmConfigService riskAlarmConfigService;

    @Resource
    private TableOperateLogService tableOperateLogService;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {

        log.info("C订单还车事件");
        String tag = message.getTag();
        byte[] body = message.getBody();
        EventEnum eventEnum = EventEnum.valueOf(tag);
        String vin = "";
        String contractId = "";
        switch (eventEnum) {
            case RETURN_VEHICLE:
                log.info("C订单还车事件KEY :" + message.getKey());
                ReturnVehicle returnVehicle = new ReturnVehicle();
                ProtobufUtil.deserializeProtobuf(body, returnVehicle);
                contractId = returnVehicle.getOrderSeq();
                vin = returnVehicle.getVin();
                break;
            case ORDER_RENEWAL:
                log.info("C订单续租事件KEY :" + message.getKey());
                OrderRenewal orderRenewal = new OrderRenewal();
                ProtobufUtil.deserializeProtobuf(body, orderRenewal);
                contractId = orderRenewal.getOrderSeq();
                vin = orderRenewal.getVin();
                break;
            default:
        }

        // 判断是否有进行中的报警
        RiskAlarm riskAlarm = tableRiskAlarmService.queryDataByVinAndType(vin, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());
        if (riskAlarm == null || !riskAlarm.getOrderSeq().equals(contractId)){
            return Action.CommitMessage;
        }

        //  自动完成车辆巡查任务
        commonService.autoCompleteSingleVehiclePatrolTask(riskAlarm);

        int maxVehicleStatus = commonService.queryMaxVehicleRiskStatus(vin, riskAlarm.getAlarmNo());
        // 解除报警
        riskAlarm.setRecoverMethod(RecoverMethodEnum.AUTO_RECOVER.getCode());
        riskAlarm.setRecoveryDate(new Date());
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
        tableRiskAlarmService.update(riskAlarm);

        saveOperateLog("订单续租/还车, 解除风险", riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode());

        // 同步更新该车所有车辆风险状态
        subAssetsService.updateVehicleRiskStatusByVin(vin, maxVehicleStatus);

        // 配置
        riskAlarmConfigService.updateRiskAlarmConfig(vin, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), new Date(), null, null, CurrentUser.newBuilder().setNickName("定时任务").setUserAccount("System").build());
        return Action.CommitMessage;
    }

    public void saveOperateLog(String operateContent, String relationKey, Integer operateType){
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(operateType);
        operateLog.setOperateContent(operateContent);
        operateLog.setCreateOperName("系统");
        operateLog.setCreateOperAccount("System");
        tableOperateLogService.save(operateLog);
    }
}
