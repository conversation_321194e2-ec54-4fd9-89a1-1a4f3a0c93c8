package com.saicmobility.evcard.vlms.risk.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.saicmobility.common.bpe.FlowMap;
import com.saicmobility.evcard.md.mddataproxy.api.GetOrgInfoByCodeForWtReq;
import com.saicmobility.evcard.md.mddataproxy.api.GetOrgInfoByCodeForWtRes;
import com.saicmobility.evcard.md.mddataproxy.api.MdDataProxy;
import com.saicmobility.evcard.utils.FileUtils;
import com.saicmobility.evcard.utils.LocalDateUtil;
import com.saicmobility.evcard.utils.PbConvertUtil;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.constant.RealFields;
import com.saicmobility.evcard.vlms.risk.constant.RedisKey;
import com.saicmobility.evcard.vlms.risk.database.*;
import com.saicmobility.evcard.vlms.risk.dto.*;
import com.saicmobility.evcard.vlms.risk.dto.check.QueryRiskCheckResponse;
import com.saicmobility.evcard.vlms.risk.dto.check.RiskCheckData;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.error.ResultCode;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.model.RiskCheckCollectionInfo;
import com.saicmobility.evcard.vlms.risk.model.*;
import com.saicmobility.evcard.vlms.risk.service.*;
import com.saicmobility.evcard.vlms.risk.service.base.BaseService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubRiskAlaramService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.LockUtil;
import com.saicmobility.evcard.vlms.risk.utils.RiskUtils;
import com.saicmobility.evcard.vlms.risk.utils.BaiYangUtil;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.AssetsVehicle;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import com.saicmobility.evcard.vlms.vlmsthirddependentservice.api.SyncVehicleDataToBaiYangReq;
import com.saicmobility.evcard.vlms.vlmsthirddependentservice.api.SyncVehicleDataToBaiYangRes;
import com.saicmobility.evcard.vlms.vlmsthirddependentservice.api.VlmsThirdDependentService;
import krpc.rpc.util.BeanToMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
@Service
@Slf4j
public class RiskCheckServiceImpl extends BaseService implements RiskCheckService {

    @Resource
    public TableRiskCheckService tableRiskCheckService;

    @Resource
    public TableRiskAlarmService tableRiskAlarmService;

    @Resource
    public TableAttachmentService tableAttachmentService;

    @Resource
    private RiskAlarmConfigService riskAlarmConfigService;
    @Resource
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;
    @Resource
    private RiskAlarmService riskAlarmService;

    @Resource
    private ExportFileService exportFileService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private RiskCommonService riskCommonService;

    @Resource
    private SubAssetsService subAssetsService;

    @Resource
    private SubRiskAlaramService subRiskAlaramService;

    @Resource
    private TableRiskCheckCollectionInfoService tableRiskCheckCollectionInfoService;

    @Resource
    private RiskCheckCompleteApproveService riskCheckCompleteApproveService;

    @Resource
    private RiskCheckCompleteApproveLinkService riskCheckCompleteApproveLinkService;

    @Resource
    private VlmsThirdDependentService vlmsThirdDependentService;

    @Resource
    private ApprovalStartService approvalStartService;

    @Resource
    private MdDataProxy mdDataProxy;

    public final List<Integer> OUT_CONTROL_TYPE_LIST = Arrays.asList(OutControlTypeEnum.VEHICLE_LOSS.getCode(), OutControlTypeEnum.VEHICLE_MISSED.getCode());

    private final Integer successStatus = 10;//白杨审批通过
    private final List<Integer> failureList = Arrays.asList(20, 30, 40); //10=审批完成 20=审批退回 30=审批拒绝 40=撤回
    private final List<Integer> ignoreList = Arrays.asList(0, 1);//0=未审批 1=审批中

    @Override
    public QueryRiskCheckRes queryRiskCheck(QueryRiskCheckReq req) {
        List<RiskCheckData> riskCheckList = tableRiskCheckService.selectList(req);
        if (riskCheckList != null) {
            for (RiskCheckData riskCheck : riskCheckList) {
                // 当前报警中风险数量
                List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(riskCheck.getVin(), AlarmStatusEnum.ALARM_STATUS.getCode(), 2);
                if (riskCheck.getDealStatus() == DealStatusEnum.DEAL_STATUS.getCode()){
                    riskCheck.setAlarmNum(riskAlarmList.size());
                }else {
                    riskCheck.setAlarmNum(0);
                }
                riskCheck.setOperateOrgName(Global.instance.configLoader.getOrgName(riskCheck.getOperateOrgId()));
                riskCheck.setOrgName(Global.instance.configLoader.getOrgName(riskCheck.getOrgId()));
                // 收车日志数量
                List<RiskCheckCollectionInfo> riskCheckCollectionInfos = tableRiskCheckCollectionInfoService.selectList(riskCheck.getId());
                riskCheck.setCollectInfoCount(CollectionUtils.isNotEmpty(riskCheckCollectionInfos) ? riskCheckCollectionInfos.size() : 0);
            }
        }
        QueryRiskCheckResponse response = new QueryRiskCheckResponse();
        response.setList(riskCheckList);
        response.setTotal(tableRiskCheckService.selectTotal(req));
        return PbConvertUtil.generateProtoBuffer(response, QueryRiskCheckRes.class);
    }

    @Override
    public QueryRiskCheckDetailRes queryRiskCheckDetail(QueryRiskCheckDetailReq req) {
        QueryRiskCheckDetailRes.Builder builder = QueryRiskCheckDetailRes.newBuilder();
        RiskCheck riskCheck = tableRiskCheckService.selectById(req.getId());
        if (riskCheck == null){
            throw new ServiceException("风控收车任务不存在");
        }
        RiskCheckData riskCheckData = new RiskCheckData();
        BeanUtils.copyProperties(riskCheck, riskCheckData);

        // 查询补充信息
        RiskCheckExtendInfo riskCheckExtend = tableRiskCheckService.selectRiskCheckExtendById(riskCheck.getId());
        if (riskCheckExtend != null){
            riskCheckData.setCollectCarDatetime(riskCheckExtend.getCollectCarDatetime());
            riskCheckData.setCollectCarPlace(riskCheckExtend.getCollectCarPlace());
            riskCheckData.setCollectCarPeople(riskCheckExtend.getCollectCarPeople());
            riskCheckData.setCollectCarType(riskCheckExtend.getCollectCarType());
            riskCheckData.setCollectCarRiskType(riskCheckExtend.getCollectCarRiskType());
            riskCheckData.setProvince(riskCheckExtend.getProvince());
            riskCheckData.setCity(riskCheckExtend.getCity());
            riskCheckData.setAddress(riskCheckExtend.getAddress());
        }
        List<Attachment> attachmentList = queryAttachment(3, String.valueOf(riskCheck.getId()));
        if (CollectionUtil.isNotEmpty(attachmentList)){
            riskCheckData.setAttachmentPaths(attachmentList);
        }

        attachmentList = queryAttachment(5, String.valueOf(riskCheck.getId()));
        if (CollectionUtil.isNotEmpty(attachmentList)){
            riskCheckData.setCheckVehicleFormInfo(attachmentList.get(0));
            riskCheckData.setCheckVehicleFormInfos(attachmentList);
        }

        attachmentList = queryAttachment(6, String.valueOf(riskCheck.getId()));
        if (CollectionUtil.isNotEmpty(attachmentList)){
            riskCheckData.setCheckVehicleVideoInfo(attachmentList.get(0));
            riskCheckData.setCheckVehicleVideoInfos(attachmentList);
        }

        attachmentList = queryAttachment(7, String.valueOf(riskCheck.getId()));
        if (CollectionUtil.isNotEmpty(attachmentList)){
            riskCheckData.setVehicleItemInfo(attachmentList.get(0));
            riskCheckData.setVehicleItemInfos(attachmentList);
        }

        // 失控附件
        riskCheckData.setOutControlAttachment(tableAttachmentService.selectById(riskCheck.getOutControlAnnexId()));
        //车辆照片/附件
        riskCheckData.setVehicleAttachment(tableAttachmentService.selectById(riskCheck.getVehiclePicId()));
        AssetsVehicle vehicleInfoByVin = getVehicleInfoByVin(riskCheck.getVin());
        if (vehicleInfoByVin != null){
            riskCheckData.setVehicleRiskStatus(vehicleInfoByVin.getVehicleRiskStatus());
            riskCheckData.setPropertyStatus(vehicleInfoByVin.getPropertyStatus());
            riskCheckData.setProductLine(vehicleInfoByVin.getProductLine());
            riskCheckData.setSubProductLine(vehicleInfoByVin.getSubProductLine());
            riskCheckData.setStoreName(getStoreName(vehicleInfoByVin.getStoreId()));
            riskCheckData.setPlateNo(vehicleInfoByVin.getPlateNo());
            riskCheckData.setOrgName(Global.instance.configLoader.getOrgName(vehicleInfoByVin.getPropertyOrgId()));
            riskCheckData.setOperateOrgName(Global.instance.configLoader.getOrgName(vehicleInfoByVin.getOperationOrgId()));
        }
        riskCheckData.setOutControlTime(riskCheck.getOutControlDate());

        //  查询最新的提交给白杨的申请编号
        RiskCheckCompleteApprove riskCheckCompleteApprove = riskCheckCompleteApproveService.selectLatestRequestNoByCheckNo(riskCheck.getCheckNo());
        if (riskCheckCompleteApprove != null) {
            String requestNo = riskCheckCompleteApprove.getRequestNo();
            riskCheckData.setLatestRequestNo(requestNo);
        } else {
            riskCheckData.setLatestRequestNo("");
        }

        // 查产品线
        SearchVehicleFileList assetsVehicle = subAssetsService.searchAssetsVehicle(riskCheck.getVin());
        // 查询收车任务编号
        // 订单号
        String orderNo = subRiskAlaramService.quertLongRentOrderByVin(riskCheck.getVin());
        if (assetsVehicle != null && assetsVehicle.getProductLine() == 2 && StringUtils.isNotBlank(orderNo)){
            JSONObject result = RiskUtils.queryLongRentTask(orderNo);
            if (result == null){
                throw new ServiceException("调用车管服务异常");
            }
            if (result.getIntValue("code") != 0){
                throw new ServiceException(result.getString("message"));
            }
            riskCheckData.setRentTaskNo(result.getString("data"));
        }

        builder.setDetail(PbConvertUtil.generateProtoBuffer(riskCheckData, RiskCheckDetail.class));
        return builder.build();
    }

    @Transactional
    @Override
    public UpgradeRiskCheckRes upgradeRiskCheck(UpgradeRiskCheckReq req) {

        // 风控收车任务
        RiskCheck riskCheck = tableRiskCheckService.selectById(req.getId());
        if (riskCheck == null){
            throw new ServiceException("风控收车任务不存在");
        }

        /*if (!OUT_CONTROL_TYPE_LIST.contains(req.getOutControlType())){
            throw new ServiceException("失控类型不存在");
        }*/
        RiskAlarm riskAlarm1 = tableRiskAlarmService.queryDataByVinAndType(riskCheck.getVin(), AlarmTypeEnum.VEHICLE_LOST.getCode());
        if (riskAlarm1 != null){
            throw new ServiceException("已存在进行中的报警类型");
        }
        // 附件上传
        AttachmentInfo outControlAttachment = req.getOutControlAttachment();
        Long attachmentId = uploadAttachment(outControlAttachment);

        // 风控收车更新
        riskCheck.setOutControlType(req.getOutControlType());
        riskCheck.setOutControlDate(new Date());
        riskCheck.setOutControlAnnexId(attachmentId);
        riskCheck.setOutControlDesc(req.getOutControlDesc());
        riskCheck.setUpdateOperAccount(req.getCurrentUser().getUserAccount());
        riskCheck.setUpdateOperName(req.getCurrentUser().getNickName());
        tableRiskCheckService.update(riskCheck);
        RiskAlarm riskAlarm = buildRiskAlarm(riskCheck, req.getCurrentUser());
        // 升级报警等级
        riskAlarmService.handelRiskAlarm(riskAlarm, RiskOperateEnum.OPERATE_ADD.getCode(),req.getCurrentUser());

        // 日志
        String logContent = StrUtil.format( "升级失控, 失控类型【{}】", OutControlTypeEnum.getValueByCode(req.getOutControlType()));
        // 风控收车日志
        saveOperateLog(logContent, riskCheck.getCheckNo(), OperateTypeEnum.OPERATE_RECEIVE_VEHICLE.getCode(), req.getCurrentUser());
        // 报警日志
        logContent = logContent + ",风控报警升级至3级";
        saveOperateLog(logContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), req.getCurrentUser());
        // 车辆日志
        saveVehicleLog(riskAlarm.getVin(), logContent, req.getCurrentUser());
        return UpgradeRiskCheckRes.ok();
    }

    private RiskAlarm buildRiskAlarm(RiskCheck riskCheck, CurrentUser currentUser) {
        Date now = new Date();
        RiskAlarm riskAlarm = new RiskAlarm();
        riskAlarm.setAlarmNo(outDescIdUtil.nextId("FKBJ"));
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS.getCode());
        riskAlarm.setAlarmLevel(AlarmLevenEnum.THREE.getCode());
        riskAlarm.setAlarmType(AlarmTypeEnum.VEHICLE_LOST.getCode());
        riskAlarm.setAlarmTime(now);
        riskAlarm.setVin(riskCheck.getVin());
        riskAlarm.setAlarmSystem(AlarmSystemEnum.WTXT.getCode());
        //riskAlarm.setPlateNo(riskCheck.getPlateNo());
        /*riskAlarm.setOrgId(riskCheck.getOrgId());
        riskAlarm.setOrgName(riskCheck.getOrgName());
        riskAlarm.setOperateOrgId(riskCheck.getOperateOrgId());
        riskAlarm.setOperateOrgName(riskCheck.getOperateOrgName());*/
        riskAlarm.setCreateOperName(currentUser.getNickName());
        riskAlarm.setCreateOperAccount(currentUser.getUserAccount());
        riskAlarm.setCreateTime(now);
        riskAlarm.setOrderSeq("");
        setGPSInfo(riskAlarm);
        return riskAlarm;

    }


    /**
     * 设置GPS信息
     * @param riskAlarm
     */
    public void setGPSInfo(RiskAlarm riskAlarm){
        VehicleGPSData vehicleGPSData = riskCommonService.fetchGpsDate(riskAlarm.getVin());
        if(null != vehicleGPSData){
            if(null != vehicleGPSData.getOldGpsDateTime()){
                riskAlarm.setLastLocationTime(new Date(vehicleGPSData.getOldGpsDateTime()));
            }
            riskAlarm.setLastLocation(vehicleGPSData.getLastAddress());
        }
    }

    @Transactional
    @Override
    public CompleteRiskCheckRes completeRiskCheck(CompleteRiskCheckReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        RiskCheck riskCheck = tableRiskCheckService.selectById(req.getId());
        if (riskCheck == null) {
            throw new ServiceException("风控收车任务不存在");
        }
        // 附件上传
        AttachmentInfo vehicleAttachment = req.getVehicleAttachement();
        Long attachmentId = uploadAttachment(vehicleAttachment);
        Date now = new Date();
        // 更新
        riskCheck.setVehiclePicId(attachmentId);
        riskCheck.setDealStatus(DealStatusEnum.DEAL_RECOVER.getCode());
        riskCheck.setTaskEndTime(now);
        riskCheck.setEndOperName(currentUser.getNickName());
        riskCheck.setEndOperAccount(currentUser.getUserAccount());
        riskCheck.setVehicleCheckDesc(req.getVehicleCheckDesc());
        riskCheck.setUpdateOperName(currentUser.getNickName());
        riskCheck.setUpdateOperAccount(currentUser.getUserAccount());
        tableRiskCheckService.update(riskCheck);

        // 保存补充信息，若任务id已存在，则更新，否则新增
        RiskCheckExtendInfo riskCheckExtend = new RiskCheckExtendInfo();
        RiskCheckExtendInfo existRiskCheckExtend = tableRiskCheckService.selectRiskCheckExtendById(riskCheck.getId());
        if (existRiskCheckExtend == null){  // 不存在，则新增
            riskCheckExtend.setRiskCheckId(riskCheck.getId());
            riskCheckExtend.setCollectCarDatetime(req.getCollectCarDatetime());
            riskCheckExtend.setCollectCarPlace(req.getCollectCarPlace());
            riskCheckExtend.setCollectCarPeople(req.getCollectCarPeople());
            riskCheckExtend.setCollectCarType(req.getCollectCarType());
            riskCheckExtend.setCollectCarRiskType(req.getCollectCarRiskType());
            riskCheckExtend.setCreateOperName(currentUser.getNickName());
            riskCheckExtend.setCreateOperAccount(currentUser.getUserAccount());
            riskCheckExtend.setUpdateOperName(currentUser.getNickName());
            riskCheckExtend.setUpdateOperAccount(currentUser.getUserAccount());
            tableRiskCheckService.insertRiskCheckExtend(riskCheckExtend);
        } else {  // 存在，则更新
            riskCheckExtend.setId(existRiskCheckExtend.getId());  // 设置主键id
            riskCheckExtend.setRiskCheckId(riskCheck.getId());
            riskCheckExtend.setCollectCarDatetime(req.getCollectCarDatetime());
            riskCheckExtend.setCollectCarPlace(req.getCollectCarPlace());
            riskCheckExtend.setCollectCarPeople(req.getCollectCarPeople());
            riskCheckExtend.setCollectCarType(req.getCollectCarType());
            riskCheckExtend.setCollectCarRiskType(req.getCollectCarRiskType());
            riskCheckExtend.setUpdateOperName(currentUser.getNickName());
            riskCheckExtend.setUpdateOperAccount(currentUser.getUserAccount());
            tableRiskCheckService.updateRiskCheckExtend(riskCheckExtend);
        }

        // 更新每个报警的开始时间
        //tableRiskAlarmConfigService.updateByVin(riskCheck.getVin());
        // 更新所有报警配置开始时间
        updateAllAlarmConfig(riskCheck.getVin(), currentUser, "完成风控收车任务，修改报警开始时间");
        // 日志
        saveOperateLog("完成风控收车任务", riskCheck.getCheckNo(), OperateTypeEnum.OPERATE_RECEIVE_VEHICLE.getCode(), currentUser);
        // 完成收车 查询车辆是否有进行中的报警 自动将该车辆的所有报警恢复
        List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(riskCheck.getVin(), AlarmStatusEnum.ALARM_STATUS.getCode(), 2);
        if (CollectionUtil.isNotEmpty(riskAlarmList)){
            for (RiskAlarm alarm : riskAlarmList) {
                RemoveRiskAlarmReq removeRiskAlarmReq = RemoveRiskAlarmReq.newBuilder().setId(alarm.getId())
                                .setCurrentUser(req.getCurrentUser()).setAutoFlag(1).setMiscDesc("风控收车").build();
                riskAlarmService.removeRiskAlarm(removeRiskAlarmReq);
            }
        }
        // 【验车单】：附件（1个），必填
        AttachmentInfo checkVehicleFormInfo = req.getCheckVehicleFormInfo();
        if (checkVehicleFormInfo == null){
            throw new ServiceException("验车单不能为空");
        }
        //【车辆清收音视频资料】：附件（1个），必填
        AttachmentInfo checkVehicleVideoInfo = req.getCheckVehicleVideoInfo();
        if (checkVehicleVideoInfo == null){
            throw new ServiceException("车辆清收音视频资料不能为空");
        }
        //【车内物品交接单】：附件（1个），必填
        AttachmentInfo vehicleItemInfo = req.getVehicleItemInfo();
        if (vehicleItemInfo == null){
            throw new ServiceException("车内物品交接单不能为空");
        }
        List<AttachmentInfo> attachmentInfoList = Lists.newArrayList();
        attachmentInfoList.add(checkVehicleFormInfo);
        batchUploadAttachment(attachmentInfoList,5, String.valueOf(riskCheck.getId()),req.getCurrentUser());

        attachmentInfoList = Lists.newArrayList();
        attachmentInfoList.add(checkVehicleVideoInfo);
        batchUploadAttachment(attachmentInfoList,6, String.valueOf(riskCheck.getId()),req.getCurrentUser());

        attachmentInfoList = Lists.newArrayList();
        attachmentInfoList.add(vehicleItemInfo);
        batchUploadAttachment(attachmentInfoList,7, String.valueOf(riskCheck.getId()),req.getCurrentUser());

        // 其他
        List<AttachmentInfo> attachmentPaths = req.getAttachmentPathsList();
        if (CollectionUtil.isNotEmpty(attachmentPaths)){
            batchUploadAttachment(attachmentPaths,3, String.valueOf(riskCheck.getId()),req.getCurrentUser());
        }

        // 查产品线
        SearchVehicleFileList assetsVehicle = subAssetsService.searchAssetsVehicle(riskCheck.getVin());
        // 订单号
        String orderNo = subRiskAlaramService.quertLongRentOrderByVin(riskCheck.getVin());
        if (assetsVehicle != null && assetsVehicle.getProductLine() == 2 && StringUtils.isNotBlank(orderNo)){
            JSONObject result = RiskUtils.completelLongRentTask(orderNo, riskCheck.getCheckNo(), "");
            if (result == null){
                throw new ServiceException("调用车管服务异常");
            }
            if (result.getIntValue("code") != 0){
                throw new ServiceException(result.getString("message"));
            }
        }
        return CompleteRiskCheckRes.ok();
    }

    @Transactional
    @Override
    public CancelRiskCheckRes cancelRiskCheck(CancelRiskCheckReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        RiskCheck riskCheck = tableRiskCheckService.selectById(req.getId());
        if (riskCheck == null) {
            throw new ServiceException("风控收车任务不存在");
        }
        if (riskCheck.getAlarmSystem().equals(AlarmSystemEnum.CZXT.getCode())){
            throw new ServiceException("长租发起的风控收车任务不能取消");
        }
        // 附件
        List<AttachmentInfo> attachmentPathsList = req.getAttachmentPathsList();
        //所有报警中的风险报警任务，全部恢复
        List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(riskCheck.getVin(), AlarmStatusEnum.ALARM_STATUS.getCode(), 2);
        if (CollectionUtil.isNotEmpty(riskAlarmList)){
            for (RiskAlarm riskAlarm : riskAlarmList) {

                riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
                riskAlarm.setRecoverMethod(RecoverMethodEnum.RECOVER_METHOD.getCode());
                riskAlarm.setRecoverChannel(AlarmSystemEnum.WTXT.getCode());
                riskAlarm.setRecoveryDate(new Date());
                riskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
                riskAlarm.setUpdateOperName(currentUser.getNickName());
                riskAlarm.setUpdateOperAccount(currentUser.getUserAccount());
                tableRiskAlarmService.update(riskAlarm);
                // 报警日志
                saveOperateLog("取消风控收车任务,更新报警开始时间", riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), currentUser);

                // 删除redis中的数据
                if (riskAlarm.getAlarmType().equals(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())){
                    Global.instance.redisUtil.del(RealFields.VEHICLE_ELECTRON_KEY + riskAlarm.getVin());
                }
            }
        }
         // 更新所有报警配置开始时间
        updateAllAlarmConfig(riskCheck.getVin(), currentUser, "取消风控收车任务,恢复报警");

        riskCheck.setDealStatus(DealStatusEnum.DEAL_RECOVER.getCode());
        riskCheck.setTaskEndTime(new Date());
        riskCheck.setEndOperName(currentUser.getNickName());
        riskCheck.setEndOperAccount(currentUser.getUserAccount());
        riskCheck.setVehicleCheckDesc(req.getOutControlDesc());
        tableRiskCheckService.update(riskCheck);

        // 更新车辆风险状态
        subAssetsService.updateVehicleRiskStatusByVin(riskCheck.getVin(), VehicleRiskStatusEnum.NO_RISK.getCode());
        // 报警日志
        saveOperateLog("取消风控收车任务", riskCheck.getCheckNo(), OperateTypeEnum.OPERATE_RECEIVE_VEHICLE.getCode(), currentUser);

        if(CollectionUtil.isNotEmpty(attachmentPathsList)){
            batchUploadAttachment(attachmentPathsList,3, String.valueOf(riskCheck.getId()),req.getCurrentUser());
        }
        return CancelRiskCheckRes.ok();
    }

    public void updateAllAlarmConfig(String vin, CurrentUser currentUser, String logContent){
        for (AlarmTypeEnum alarmTypeEnum : AlarmTypeEnum.values()) {
            // 修改风控报警配置
            Long configId = riskAlarmConfigService.updateRiskAlarmConfig(vin, alarmTypeEnum.getCode(), new Date(), null, null, currentUser);
            // 报警日志
            saveOperateLog(logContent, configId+"",OperateTypeEnum.OPERATE_CONFIG.getCode() ,currentUser);
        }
    }

    @Override
    public void autoCreateRiskCheck(RiskAlarm riskAlarm, CurrentUser currentUser) {

        RiskCheck query = new RiskCheck();
        query.setVin(riskAlarm.getVin());
        query.setDealStatus(DealStatusEnum.DEAL_STATUS.getCode());
        List<RiskCheck> riskChecks = tableRiskCheckService.selectBy(query);
        // 如果有进行中的收车任务不用创建
        if (CollectionUtil.isNotEmpty(riskChecks)){
            return;
        }
        // 新增风控收车任务
        RiskCheck riskCheck = riskCheckBuilder(riskAlarm, currentUser);
        tableRiskCheckService.insert(riskCheck);

        saveOperateLog("创建风控收车任务", riskCheck.getCheckNo(), OperateTypeEnum.OPERATE_RECEIVE_VEHICLE.getCode(), currentUser);

    }

    @Override
    public QueryRiskCheckRes exportRiskCheck(QueryRiskCheckReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        long total = tableRiskCheckService.selectTotal(req);
        if (total == 0){
            throw new ServiceException(ResultCode.Fail, "没有符合条件的风控收车不能导出");
        }

        //文件根路径
        String mfsRootPath = Global.instance.getMfsRootPath();
        //文件下载地址
        String projectDownloadUrl = Global.instance.getProjectDownloadUrl();

        String fileName = ExcelTypeEnum.EXCEL_TYPE_2.getValue()+"_"+ LocalDateUtil.format(new Date(), LocalDateUtil.UNSIGNED_DATETIME_PATTERN) +".xlsx";
        String templateFile = "template/"+ExcelTypeEnum.EXCEL_TYPE_2.getTemplate()+".xlsx";
        // 开始导出
        String fileName1 = ExcelTypeEnum.EXCEL_TYPE_2.getValue();
        String exportFilePath = FileUtils.getFilePathSuffix(fileName1,1);
        String fullExportFilePath = mfsRootPath + exportFilePath;
        String downLoadFilePath = projectDownloadUrl + exportFilePath;

        Integer exportFileId = exportFileService.startExport(FileSourceEnum.FILE_SOURCE_16.getSource(), fileName, downLoadFilePath, currentUser.getNickName(),
                currentUser.getUserAccount(), currentUser.getOrgId(), currentUser.getOrgName());
        if (exportFileId == null){
            throw new ServiceException("创建导出文件失败");
        }
        // 异步导出
        taskExecutor.execute(()->{
            // 每页条数
            int pageSize = 5000;
            int totalPageNum = ((int)total + pageSize - 1) / pageSize;
            int pageNum = 1;

            List<RiskCheckData> riskCheckDataList = new ArrayList<>();
            do {
                QueryRiskCheckReq build = req.toBuilder().setPageNum(pageNum).setPageSize(pageSize).build();
                List<RiskCheckData> riskChecks = tableRiskCheckService.selectList(build);
                if (CollectionUtil.isEmpty(riskChecks)){
                    break;
                }
                for (RiskCheckData riskCheckData : riskChecks) {

                    riskCheckData.setDealStatusDesc(DealStatusEnum.getValueByCode(riskCheckData.getDealStatus()));
                    riskCheckData.setCreateTimeStr(DateUtil.dateToString(riskCheckData.getCreateTime(), DateUtil.DATE_TYPE1));
                    riskCheckData.setTaskEndTimeStr(DateUtil.dateToString(riskCheckData.getTaskEndTime(), DateUtil.DATE_TYPE1));
                    riskCheckData.setVehicleRiskStatusDesc(VehicleRiskStatusEnum.getValueByCode(riskCheckData.getVehicleRiskStatus()));
                    // 当前报警中风险数量
                    List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(riskCheckData.getVin(), AlarmStatusEnum.ALARM_STATUS.getCode(), 2);
                    if (riskCheckData.getDealStatus() == DealStatusEnum.DEAL_STATUS.getCode()){
                        riskCheckData.setAlarmNum(riskAlarmList.size());
                    }else {
                        riskCheckData.setAlarmNum(0);
                    }
                    // 当前收车任务所有二级报警
                    if (riskCheckData.getDealStatus().equals(DealStatusEnum.DEAL_STATUS.getCode())){
                        riskCheckData.setCurrentAlarmType(getCurrentAlarmType(riskAlarmList));
                    }
                    riskCheckData.setOrgName(Global.instance.configLoader.getOrgName(riskCheckData.getOrgId()));
                    riskCheckData.setOperateOrgName(Global.instance.configLoader.getOrgName(riskCheckData.getOperateOrgId()));
                    riskCheckData.setOutControlTypeDesc(OutControlTypeEnum.getValueByCode(riskCheckData.getOutControlType()));

                    AssetsVehicle vehicleInfoByVin = getVehicleInfoByVin(riskCheckData.getVin());
                    if (vehicleInfoByVin != null){
                        riskCheckData.setPropertyStatusDesc(PropertyStatusEnums.getValueByCode(vehicleInfoByVin.getPropertyStatus()));
                        riskCheckData.setProductLineDesc(PropertyStatusEnums.getValueByCode(vehicleInfoByVin.getProductLine()));
                        riskCheckData.setSubProductLineDesc(SubProductLineEnum.getValueByCode(vehicleInfoByVin.getSubProductLine()));
                        riskCheckData.setStoreName(getStoreName(vehicleInfoByVin.getStoreId()));
                    }
                    // 收车日志数量
                    List<RiskCheckCollectionInfo> riskCheckCollectionInfos = tableRiskCheckCollectionInfoService.selectList(riskCheckData.getId());
                    riskCheckData.setCollectInfoCount(CollectionUtils.isNotEmpty(riskCheckCollectionInfos) ? riskCheckCollectionInfos.size() : 0);
                }
                riskCheckDataList.addAll(riskChecks);
                pageNum ++;
            }while (totalPageNum >= pageNum);

            //如果目录不存在，则创建目录
            File file = new File(fullExportFilePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            boolean noResult = ObjectUtil.isNull(riskCheckDataList);
            try (
                    InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream(templateFile);
                    FileOutputStream fileOutputStream = new FileOutputStream(fullExportFilePath);
                    ExcelWriter excelWriter = EasyExcelFactory.write(fileOutputStream).withTemplate(templateStream).build();

            ) {
                WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                //无数据处理
                if(noResult){
                    excelWriter.fill(new ArrayList<>(), writeSheet);
                }else {
                    excelWriter.fill(riskCheckDataList, writeSheet);
                }
                fileOutputStream.flush();
                excelWriter.finish();
                //导出成功
                exportFileService.exportSuccess(exportFileId, currentUser.getNickName());
            }catch (Exception e) {
                e.printStackTrace();
                log.error(e.getLocalizedMessage(), e);
                //导出失败
                exportFileService.exportFail(exportFileId, e.getLocalizedMessage(), currentUser.getNickName());
            }
        });
        return QueryRiskCheckRes.ok();
    }

    /**
     * 完成任务-提交白杨审批
     */
    @Transactional
    @Override
    public CompleteRiskCheckRes submitRiskCheckToBaiYang(CompleteRiskCheckReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        RiskCheck riskCheck = tableRiskCheckService.selectById(req.getId());
        if (riskCheck == null) {
            throw new ServiceException("风控收车任务不存在");
        }

        // 【验车单】：附件（3个），必填
        //AttachmentInfo checkVehicleFormInfo = req.getCheckVehicleFormInfo();
        List<AttachmentInfo> checkVehicleFormInfosList = req.getCheckVehicleFormInfosList();
        if (CollectionUtils.isEmpty(checkVehicleFormInfosList)){
            throw new ServiceException("验车单不能为空");
        }
        if (checkVehicleFormInfosList.size() > 3){
            throw new ServiceException("验车单不能超过3个");
        }
        //【车辆清收音视频资料】：附件（3个），必填
        //AttachmentInfo checkVehicleVideoInfo = req.getCheckVehicleVideoInfo();
        List<AttachmentInfo> checkVehicleVideoInfosLists = req.getCheckVehicleVideoInfosList();
        if (CollectionUtils.isEmpty(checkVehicleVideoInfosLists)){
            throw new ServiceException("车辆清收音视频资料不能为空");
        }
        if (checkVehicleVideoInfosLists.size() > 3){
            throw new ServiceException("车辆清收音视频资料不能超过3个");
        }
        //【车内物品交接单】：附件（3个），必填
        //AttachmentInfo vehicleItemInfo = req.getVehicleItemInfo();
        List<AttachmentInfo> vehicleItemInfosList = req.getVehicleItemInfosList();
        if (CollectionUtils.isEmpty(vehicleItemInfosList)){
            throw new ServiceException("车内物品交接单不能为空");
        }
        if (vehicleItemInfosList.size() > 3){
            throw new ServiceException("车内物品交接单不能超过1个");
        }

        // 附件上传
        AttachmentInfo vehicleAttachment = req.getVehicleAttachement();
        Long attachmentId = uploadAttachment(vehicleAttachment);
        // 更新
        riskCheck.setVehiclePicId(attachmentId);
        riskCheck.setDealStatus(DealStatusEnum.DEAL_APPROVAL_PENDING.getCode());
        riskCheck.setVehicleCheckDesc(req.getVehicleCheckDesc());
        riskCheck.setUpdateOperName(currentUser.getNickName());
        riskCheck.setUpdateOperAccount(currentUser.getUserAccount());
        riskCheck.setIsAutoComplete(req.getIsAutoComplete());
        tableRiskCheckService.update(riskCheck);

        // 保存补充信息，若任务id已存在，则更新，否则新增
        RiskCheckExtendInfo riskCheckExtend = new RiskCheckExtendInfo();
        RiskCheckExtendInfo existRiskCheckExtend = tableRiskCheckService.selectRiskCheckExtendById(riskCheck.getId());
        if (existRiskCheckExtend == null){  // 不存在，则新增
            riskCheckExtend.setRiskCheckId(riskCheck.getId());
            riskCheckExtend.setCollectCarDatetime(req.getCollectCarDatetime());
            riskCheckExtend.setCollectCarPlace(req.getCollectCarPlace());
            riskCheckExtend.setCollectCarPeople(req.getCollectCarPeople());
            riskCheckExtend.setCollectCarType(req.getCollectCarType());
            riskCheckExtend.setCollectCarRiskType(req.getCollectCarRiskType());
            riskCheckExtend.setCreateOperName(currentUser.getNickName());
            riskCheckExtend.setCreateOperAccount(currentUser.getUserAccount());
            riskCheckExtend.setUpdateOperName(currentUser.getNickName());
            riskCheckExtend.setUpdateOperAccount(currentUser.getUserAccount());
            riskCheckExtend.setProvince(req.getProvince());
            riskCheckExtend.setCity(req.getCity());
            riskCheckExtend.setAddress(req.getAddress());
            tableRiskCheckService.insertRiskCheckExtend(riskCheckExtend);
        } else {  // 存在，则更新
            riskCheckExtend.setId(existRiskCheckExtend.getId());  // 设置主键id
            riskCheckExtend.setRiskCheckId(riskCheck.getId());
            riskCheckExtend.setCollectCarDatetime(req.getCollectCarDatetime());
            riskCheckExtend.setCollectCarPlace(req.getCollectCarPlace());
            riskCheckExtend.setCollectCarPeople(req.getCollectCarPeople());
            riskCheckExtend.setCollectCarType(req.getCollectCarType());
            riskCheckExtend.setCollectCarRiskType(req.getCollectCarRiskType());
            riskCheckExtend.setUpdateOperName(currentUser.getNickName());
            riskCheckExtend.setUpdateOperAccount(currentUser.getUserAccount());
            riskCheckExtend.setProvince(req.getProvince());
            riskCheckExtend.setCity(req.getCity());
            riskCheckExtend.setAddress(req.getAddress());
            tableRiskCheckService.updateRiskCheckExtend(riskCheckExtend);
        }

        // 日志
        saveOperateLog("完成风控收车任务提交白杨审批", riskCheck.getCheckNo(), OperateTypeEnum.OPERATE_RECEIVE_VEHICLE.getCode(), currentUser);

        // 验车单
        List<AttachmentInfo> attachmentInfoList = Lists.newArrayList();
        attachmentInfoList.addAll(checkVehicleFormInfosList);
        //  先删除历史附件
        deleteAttachmentByFileType(5,String.valueOf(riskCheck.getId()));
        //  再上传新的附件
        batchUploadAttachment(attachmentInfoList,5, String.valueOf(riskCheck.getId()),req.getCurrentUser());

        // 车辆清收音视频资料
        attachmentInfoList = Lists.newArrayList();
        attachmentInfoList.addAll(checkVehicleVideoInfosLists);
        //  先删除历史附件
        deleteAttachmentByFileType(6,String.valueOf(riskCheck.getId()));
        //  再上传新的附件
        batchUploadAttachment(attachmentInfoList,6, String.valueOf(riskCheck.getId()),req.getCurrentUser());

        // 车内物品交接单
        attachmentInfoList = Lists.newArrayList();
        attachmentInfoList.addAll(vehicleItemInfosList);
        //  先删除历史附件
        deleteAttachmentByFileType(7,String.valueOf(riskCheck.getId()));
        //  再上传新的附件
        batchUploadAttachment(attachmentInfoList,7, String.valueOf(riskCheck.getId()),req.getCurrentUser());

        // 其他
        List<AttachmentInfo> attachmentPaths = req.getAttachmentPathsList();
        if (CollectionUtil.isNotEmpty(attachmentPaths)){
            //  先删除历史附件
            deleteAttachmentByFileType(3,String.valueOf(riskCheck.getId()));
            //  再上传新的附件
            batchUploadAttachment(attachmentPaths,3, String.valueOf(riskCheck.getId()),req.getCurrentUser());
        }

        RiskCheckData riskCheckData = new RiskCheckData();
        BeanUtils.copyProperties(riskCheck, riskCheckData);
        riskCheckData.setCollectCarDatetime(riskCheckExtend.getCollectCarDatetime());
        riskCheckData.setCollectCarPlace(riskCheckExtend.getCollectCarPlace());
        riskCheckData.setCollectCarPeople(riskCheckExtend.getCollectCarPeople());
        riskCheckData.setCollectCarType(riskCheckExtend.getCollectCarType());
        riskCheckData.setCollectCarRiskType(riskCheckExtend.getCollectCarRiskType());

        // 失控附件
        riskCheckData.setOutControlAttachment(tableAttachmentService.selectById(riskCheck.getOutControlAnnexId()));
        //车辆照片/附件
        riskCheckData.setVehicleAttachment(tableAttachmentService.selectById(riskCheck.getVehiclePicId()));
        AssetsVehicle vehicleInfoByVin = getVehicleInfoByVin(riskCheck.getVin());
        if (vehicleInfoByVin != null){
            riskCheckData.setVehicleRiskStatus(vehicleInfoByVin.getVehicleRiskStatus());
            riskCheckData.setPropertyStatus(vehicleInfoByVin.getPropertyStatus());
            riskCheckData.setProductLine(vehicleInfoByVin.getProductLine());
            riskCheckData.setSubProductLine(vehicleInfoByVin.getSubProductLine());
            riskCheckData.setStoreName(getStoreName(vehicleInfoByVin.getStoreId()));
            riskCheckData.setPlateNo(vehicleInfoByVin.getPlateNo());
            riskCheckData.setOrgName(Global.instance.configLoader.getOrgName(vehicleInfoByVin.getPropertyOrgId()));
            riskCheckData.setOperateOrgName(Global.instance.configLoader.getOrgName(vehicleInfoByVin.getOperationOrgId()));
        }
        riskCheckData.setOutControlTime(riskCheck.getOutControlDate());

        //  提交白杨审批
        BaiYangRiskCheckCompleteRequest request = new BaiYangRiskCheckCompleteRequest();
        BeanUtils.copyProperties(req, request);
        //  生成一个审批单号
        String requestNo = "wcrw-" + currentUser.getOrgId() + "-" + System.currentTimeMillis();
        request.setRequestNo(requestNo);

        FlowMap userFlowMap = Global.instance.configLoader.getUserInfo(currentUser.getUserAccount(),false);
        if (userFlowMap == null) {
            throw new ServiceException(ResultCode.COMMON_FAIL, StrUtil.format("查询用户信息失败:未获取到用户信息"));
        }
        String userId = userFlowMap.ns("userId");
        String deptCode = userFlowMap.ns("deptCode");
        String compCode = userFlowMap.ns("compCode");

        String sapCode = getSapCode(currentUser.getOrgId());

        DeliverMasterRecord masterRecord = new DeliverMasterRecord();
        //组装数据
        masterRecord.setDocumentNum(request.getRequestNo());
        masterRecord.setApplicationTitle("风控收车完成任务审批单");
        masterRecord.setApplyBy(userId);
        masterRecord.setSubmitBy(userId);
        masterRecord.setDeptCode(deptCode);
        masterRecord.setCompCode(compCode);
        masterRecord.setCompSapCode(sapCode);
        masterRecord.setSituationDescription("风控收车完成任务审批单");
        masterRecord.setBranchId(sapCode);
        masterRecord.setBranchName(riskCheckData.getOrgName());

        //  业务数据
        masterRecord.setCheckNo(riskCheckData.getCheckNo());
        masterRecord.setCreateTimes(DateUtil.dateToString(riskCheckData.getCreateTime(), DateUtil.DATE_TYPE1));
        String createOperNameAndAccount = riskCheckData.getCreateOperName();
        if (StrUtil.isNotBlank(riskCheckData.getCreateOperAccount())) {
            createOperNameAndAccount += "（" + riskCheckData.getCreateOperAccount() + "）";
        }
        masterRecord.setCreateOperNameAndAccount(createOperNameAndAccount);
        masterRecord.setVehicleRiskStatus(riskCheckData.getVehicleRiskStatus());
        masterRecord.setPlateNo(riskCheckData.getPlateNo());
        masterRecord.setVin(riskCheckData.getVin());
        masterRecord.setOrgName(riskCheckData.getOrgName());
        masterRecord.setOperateOrgName(riskCheckData.getOperateOrgName());
        masterRecord.setPropertyStatus(riskCheckData.getPropertyStatus());
        masterRecord.setProductLine(riskCheckData.getProductLine());
        masterRecord.setLastLocation(riskCheckData.getLastLocation());
        masterRecord.setLastLocationTime(DateUtil.dateToString(riskCheckData.getLastLocationTime(), DateUtil.DATE_TYPE1));
        masterRecord.setOutControlType(riskCheckData.getOutControlType());
        masterRecord.setOutControlTime(DateUtil.dateToString(riskCheckData.getOutControlTime(), DateUtil.DATE_TYPE1));
        if (riskCheckData.getOutControlAttachment() != null) {
            masterRecord.setOutControlAttachment(BaiYangUtil.getFilePathMap(riskCheckData.getOutControlAttachment().getFilePath(), riskCheckData.getOutControlAttachment().getFileName(), requestNo));
        }
        masterRecord.setOutControlDesc(riskCheckData.getOutControlDesc());
        masterRecord.setCollectCarDatetime(riskCheckData.getCollectCarDatetime());
        masterRecord.setCollectCarPlace(riskCheckData.getCollectCarPlace());
        masterRecord.setCollectCarPeople(riskCheckData.getCollectCarPeople());
        masterRecord.setCollectCarType(riskCheckData.getCollectCarType());
        masterRecord.setCollectCarRiskType(riskCheckData.getCollectCarRiskType());
        masterRecord.setIsAutoComplete(req.getIsAutoComplete());
        masterRecord.setRentTaskNo(req.getRentTaskNo());

        masterRecord.setCheckVehicleFormInfo(BaiYangUtil.getFilePathMapFromAttachmentInfoList(checkVehicleFormInfosList, requestNo));
        masterRecord.setCheckVehicleVideoInfo(BaiYangUtil.getFilePathMapFromAttachmentInfoList(checkVehicleVideoInfosLists, requestNo));
        masterRecord.setVehicleItemInfo(BaiYangUtil.getFilePathMapFromAttachmentInfoList(vehicleItemInfosList, requestNo));
        if (CollectionUtil.isNotEmpty(attachmentPaths)){
            masterRecord.setAttachmentPaths(BaiYangUtil.getFilePathMapFromAttachmentInfoList(attachmentPaths, requestNo));
        }
        masterRecord.setVehicleCheckDesc(riskCheckData.getVehicleCheckDesc());

        BaiYangRequestMasterData masterData = new BaiYangRequestMasterData();
        masterData.setDataRoutingKey(Global.instance.getBaiYangRiskCheckCompleteKey());
        masterData.setContent(JSONObject.toJSONString(masterRecord));

        //请求body
        BaiYangRequestBody requestBody = new BaiYangRequestBody();
        requestBody.setRequestNo(request.getRequestNo());
        requestBody.setData(masterData);
        requestBody.setDocType(Global.instance.getBaiYangRiskCheckCompleteKey());

        log.info("白杨请求body:{}", JSONObject.toJSONString(requestBody));
        SyncVehicleDataToBaiYangRes baiYangRes = this.getBaiYangRes(requestBody);
        //返回的白杨的单号
        String applicationId = baiYangRes.getApplicationId();

        Date date = new Date();
        RiskCheckCompleteApprove newApprove = new RiskCheckCompleteApprove();
        newApprove.setApplicationId(applicationId);
        newApprove.setRequestNo(requestNo);
        newApprove.setUserName(currentUser.getNickName());
        newApprove.setCreateOperAccount(currentUser.getUserAccount());
        newApprove.setCreateOperName(currentUser.getNickName());
        newApprove.setCreateTime(date);
        newApprove.setUpdateOperAccount(currentUser.getUserAccount());
        newApprove.setUpdateOperName(currentUser.getNickName());
        newApprove.setUpdateTime(date);
        riskCheckCompleteApproveService.insertSelective(newApprove);

        RiskCheckCompleteApproveLink riskCheckCompleteApproveLink = new RiskCheckCompleteApproveLink();
        riskCheckCompleteApproveLink.setApplicationId(applicationId);
        riskCheckCompleteApproveLink.setCheckNo(riskCheck.getCheckNo());
        riskCheckCompleteApproveLinkService.insertSelective(riskCheckCompleteApproveLink);

        return CompleteRiskCheckRes.ok();
    }

    /**
     * 完成任务-白杨回调
     */
    @Override
    public SubmitRiskCheckApprovalResultRes submitRiskCheckApprovalResult(SubmitRiskCheckApprovalResultReq req) {
        log.info("白杨审批状态回调入参：" + req.toString());
        Integer status = req.getStatus();
        String applicationId = req.getApplicationId();

        //  根据【审批编号+审批结果】上锁，锁1秒就释放
        String key = RedisKey.STOCK_RISK_CHECK_APPROVAL_NOTIFY_REDIS_KEY_PREFIX + applicationId;
        try {
            if (LockUtil.tryLock(key, 1, 1, TimeUnit.SECONDS)) {
                if (successStatus.equals(status)) {  // 审批通过
                    this.submitRiskCheckApprovalPass(applicationId);
                }
                //  审批拒绝，修改风控收车状态为执行中（供后续再次发起审批）
                if (failureList.contains(status)) {
                    this.submitRiskCheckApprovalReject(applicationId);
                }
                riskCheckCompleteApproveService.updateApproveStatus(applicationId, status);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
            log.error("风控收车完成任务白杨回调redis锁异常：{}", e.getMessage());
            return SubmitRiskCheckApprovalResultRes.failed(-2557001, "白杨审批状态回调异常");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("白杨审批状态回调异常: submitRiskCheckApprovalResult " + e.getMessage(), e);
            return SubmitRiskCheckApprovalResultRes.failed(-2557001, "白杨审批状态回调异常");
        } finally {
            LockUtil.unlock(key);
        }
        return SubmitRiskCheckApprovalResultRes.ok();
    }

    /**
     * 完成任务-白杨审批通过
     * @param applicationId
     */
    @Override
    @Transactional
    public void submitRiskCheckApprovalPass(String applicationId) {
        //	1.根据applicationId查询表1，没有则失败
        RiskCheckCompleteApprove riskCheckCompleteApprove = riskCheckCompleteApproveService.selectByApplicationId(applicationId);
        if (riskCheckCompleteApprove == null) {
            throw new ServiceException("风控收车白杨审批单表没有该applicationId：" + applicationId);
        }
        //	2.根据applicationId查询表2，没有则失败
        List<RiskCheckCompleteApproveLink> riskCheckCompleteApproveLinkList = riskCheckCompleteApproveLinkService.selectByApplicationId(applicationId);
        if (CollectionUtils.isEmpty(riskCheckCompleteApproveLinkList)) {
            throw new ServiceException("风控收车白杨审批单关联表没有该applicationId：" + applicationId);
        }

        String checkNo = riskCheckCompleteApproveLinkList.get(0).getCheckNo();

        /****************  执行原来的风控收车完成任务的操作  *******************/
        //  根据风控收车任务编号查询任务详情
        RiskCheck query = new RiskCheck();
        query.setCheckNo(checkNo);
        List<RiskCheck> riskCheckList = tableRiskCheckService.selectBy(query);
        if (CollectionUtils.isEmpty(riskCheckList)) {
            throw new ServiceException("调用车管服务异常");
        }
        RiskCheck riskCheck = riskCheckList.get(0);

        String returnCarTime = StringUtils.EMPTY;
        RiskCheckExtendInfo riskCheckExtendInfo = tableRiskCheckService.selectRiskCheckExtendById(riskCheck.getId());
        if (riskCheckExtendInfo != null && riskCheckExtendInfo.getCollectCarDatetime() != null){
            returnCarTime = riskCheckExtendInfo.getCollectCarDatetime() + " 00:00:00";
        }

        if (!Objects.equals(riskCheck.getDealStatus(), DealStatusEnum.DEAL_APPROVAL_PENDING.getCode())) {
            throw new ServiceException("风控收车任务未处于【审批中】状态，不可再次发起回调");
        }

        // 更新处理状态
        riskCheck.setDealStatus(DealStatusEnum.DEAL_RECOVER.getCode());
        riskCheck.setUpdateOperName(riskCheckCompleteApprove.getCreateOperName());
        riskCheck.setUpdateOperAccount(riskCheckCompleteApprove.getCreateOperAccount());
        tableRiskCheckService.update(riskCheck);

        CurrentUser currentUser = CurrentUser.newBuilder()
                .setNickName(riskCheckCompleteApprove.getCreateOperName())
                .setUserAccount(riskCheckCompleteApprove.getCreateOperAccount())
                .build();

        // 更新所有报警配置开始时间
        updateAllAlarmConfig(riskCheck.getVin(), currentUser, "完成风控收车任务，修改报警开始时间");
        // 日志
        saveOperateLog("风控收车任务白杨审批通过，完成风控收车任务", checkNo, OperateTypeEnum.OPERATE_RECEIVE_VEHICLE.getCode(), currentUser);
        // 完成收车 查询车辆是否有进行中的报警 自动将该车辆的所有报警恢复
        List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(riskCheck.getVin(), AlarmStatusEnum.ALARM_STATUS.getCode(), 2);
        if (CollectionUtil.isNotEmpty(riskAlarmList)){
            for (RiskAlarm alarm : riskAlarmList) {
                RemoveRiskAlarmReq removeRiskAlarmReq = RemoveRiskAlarmReq.newBuilder().setId(alarm.getId())
                        .setCurrentUser(currentUser).setAutoFlag(1).setMiscDesc("风控收车").build();
                riskAlarmService.removeRiskAlarm(removeRiskAlarmReq);
            }
        }

        // 查产品线
        SearchVehicleFileList assetsVehicle = subAssetsService.searchAssetsVehicle(riskCheck.getVin());
        // 订单号
        String orderNo = subRiskAlaramService.quertLongRentOrderByVin(riskCheck.getVin());
        if (assetsVehicle != null && assetsVehicle.getProductLine() == 2 && StringUtils.isNotBlank(orderNo) && riskCheck.getIsAutoComplete() == 1){
            JSONObject result = RiskUtils.completelLongRentTask(orderNo, riskCheck.getCheckNo(), returnCarTime);
            if (result == null){
                throw new ServiceException("调用车管服务异常");
            }
            if (result.getIntValue("code") != 0){
                throw new ServiceException(result.getString("message"));
            }
        }
    }

    /**
     * 完成任务-白杨审批拒绝
     * 修改风控收车状态为执行中（供后续再次发起审批）
     * @param applicationId
     */
    @Override
    @Transactional
    public void submitRiskCheckApprovalReject(String applicationId) {
        //	1.根据applicationId查询表1，没有则失败
        RiskCheckCompleteApprove riskCheckCompleteApprove = riskCheckCompleteApproveService.selectByApplicationId(applicationId);
        if (riskCheckCompleteApprove == null) {
            throw new ServiceException("风控收车白杨审批单表没有该applicationId：" + applicationId);
        }
        //	2.根据applicationId查询表2，没有则失败
        List<RiskCheckCompleteApproveLink> riskCheckCompleteApproveLinkList = riskCheckCompleteApproveLinkService.selectByApplicationId(applicationId);
        if (CollectionUtils.isEmpty(riskCheckCompleteApproveLinkList)) {
            throw new ServiceException("风控收车白杨审批单关联表没有该applicationId：" + applicationId);
        }

        String checkNo = riskCheckCompleteApproveLinkList.get(0).getCheckNo();

        //  根据风控收车任务编号查询任务详情
        RiskCheck query = new RiskCheck();
        query.setCheckNo(checkNo);
        List<RiskCheck> riskCheckList = tableRiskCheckService.selectBy(query);
        if (CollectionUtils.isEmpty(riskCheckList)) {
            throw new ServiceException("调用车管服务异常");
        }
        RiskCheck riskCheck = riskCheckList.get(0);

        if (!Objects.equals(riskCheck.getDealStatus(), DealStatusEnum.DEAL_APPROVAL_PENDING.getCode())) {
            throw new ServiceException("风控收车任务未处于【审批中】状态，不可再次发起回调");
        }

        // 更新处理状态
        riskCheck.setDealStatus(DealStatusEnum.DEAL_STATUS.getCode());
        riskCheck.setUpdateOperName(riskCheckCompleteApprove.getCreateOperName());
        riskCheck.setUpdateOperAccount(riskCheckCompleteApprove.getCreateOperAccount());
        tableRiskCheckService.update(riskCheck);

        CurrentUser currentUser = CurrentUser.newBuilder()
                .setNickName(riskCheckCompleteApprove.getCreateOperName())
                .setUserAccount(riskCheckCompleteApprove.getCreateOperAccount())
                .build();

        // 日志
        saveOperateLog("风控收车任务白杨审批拒绝，恢复至执行中状态", checkNo, OperateTypeEnum.OPERATE_RECEIVE_VEHICLE.getCode(), currentUser);
    }

    /**
     * 获取当前二级报警类型
     * @param riskAlarmList
     * @return
     */
    private String getCurrentAlarmType(List<RiskAlarm> riskAlarmList) {
        if (CollectionUtil.isEmpty(riskAlarmList)){
            return null;
        }
        List<String> alarms = new ArrayList<>();
        for (RiskAlarm riskAlarm : riskAlarmList) {
            if (riskAlarm.getAlarmLevel() == 2 && riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_STATUS.getCode())){
                Integer alarmType = riskAlarm.getAlarmType();
                alarms.add(AlarmTypeEnum.getValueByCode(alarmType));
            }
        }
        if (CollectionUtil.isEmpty(alarms)){
            return null;
        }
        String currentAlarmType = StringUtils.join(alarms, "、");
        return currentAlarmType;
    }

    public RiskCheck riskCheckBuilder(RiskAlarm riskAlarm, CurrentUser currentUser) {
        RiskCheck riskCheck = new RiskCheck();
        BeanUtils.copyProperties(riskAlarm, riskCheck);
        if (riskAlarm.getAlarmSystem().equals(AlarmSystemEnum.CZXT.getCode())){
            riskCheck.setAlarmSystem(AlarmSystemEnum.CZXT.getCode());
        }
        riskCheck.setId(null);
        riskCheck.setCheckNo(outDescIdUtil.nextId("FKSC"));
        riskCheck.setDealStatus(DealStatusEnum.DEAL_STATUS.getCode());
        riskCheck.setCreateTime(new Date());
        riskCheck.setUpdateTime(new Date());
        if(null != currentUser){
            riskCheck.setCreateOperName(currentUser.getNickName());
            riskCheck.setCreateOperAccount(currentUser.getUserAccount());
        }else {
            riskCheck.setCreateOperName("定时任务");
            riskCheck.setCreateOperAccount("");
        }
        return riskCheck;
    }

    private SyncVehicleDataToBaiYangRes getBaiYangRes(BaiYangRequestBody requestBody) {
        SyncVehicleDataToBaiYangReq baiYangReq = BeanToMessage.toMessage(SyncVehicleDataToBaiYangReq.class, requestBody);
        SyncVehicleDataToBaiYangRes baiYangRes = vlmsThirdDependentService.syncVehicleDataToBaiYang(baiYangReq);
        if (baiYangRes.getRetCode() < 0) {
            throw new ServiceException(ResultCode.COMMON_FAIL, baiYangRes.getRetMsg());
        }
        return baiYangRes;
    }

    public String getSapCode(String orgCode){
        if (StringUtils.isBlank(orgCode)){
            return "";
        }
        GetOrgInfoByCodeForWtRes orgInfoByCodeForWtRes = mdDataProxy
                .getOrgInfoByCodeForWt(GetOrgInfoByCodeForWtReq.newBuilder().setOrgId(orgCode).build());
        if (orgInfoByCodeForWtRes.getRetCode() != 0) {
            throw new ServiceException(ResultCode.COMMON_FAIL, "获取sapCode失败");
        }
        return orgInfoByCodeForWtRes.getOrgInfo().getSapCode();
    }
}
