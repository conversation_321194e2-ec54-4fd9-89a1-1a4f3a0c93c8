package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.dto.riskAlarmConfig.RiskAlarmConfigData;
import com.saicmobility.evcard.vlms.risk.enums.AlarmTypeEnum;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmConfigService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.AddRiskAlarmConfiReq;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskAlarmConfigReq;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自动新增车辆告警配置
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Slf4j
@JobHandler(value = "autoAddRiskAlarmConfigHandler")
@Component
public class AutoAddRiskAlarmConfigHandler extends IJobHandler {

    // ==================== 常量定义 ====================

    /**
     * 分页查询大小 - 优化后的批量处理大小
     */
    private static final int PAGE_SIZE = 2000;

    @Resource
    private RiskAlarmConfigService riskAlarmConfigService;

    @Resource
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;

    @Override
    public ReturnT<String> execute(String alarmTypeString) throws Exception {
        log.info("autoAddRiskAlarmConfigHandler:开始执行任务!");
        if (StringUtils.isNumeric(alarmTypeString)) {
            Integer alarmType = Integer.parseInt(alarmTypeString);
            if (!AlarmTypeEnum.getCodeList().contains(alarmType)) {
                log.error("参数错误，请输入正确的告警类型!");
            }
            log.info("处理告警类型: {}", alarmType);
            processVehiclesInBatches(alarmType);
        }
        log.info("autoAddRiskAlarmConfigHandler:结束执行任务!");
        return ReturnT.SUCCESS;
    }

    /**
     * 分批处理车辆
     *
     * @param alarmType 查询条件VIN
     */
    private void processVehiclesInBatches(Integer alarmType) {
        int currentPageNumber = 1;
        List<RiskAlarmConfigData> currentBatchVehicles;

        do {
            currentBatchVehicles = queryVehicleConfigurationsBatch(currentPageNumber);

            if (CollectionUtil.isEmpty(currentBatchVehicles)) {
                log.info("第{}页未查询到车辆配置数据，结束处理", currentPageNumber);
                break;
            }

            log.info("开始处理第{}页车辆，数量: {}", currentPageNumber, currentBatchVehicles.size());

            processVehicleBatch(currentBatchVehicles, alarmType);

            currentPageNumber++;

        } while (currentBatchVehicles.size() == PAGE_SIZE);
    }

    /**
     * 查询车辆配置批次数据（如果车辆有OUT_OF_ELECTRONIC_FENCE，则补全目标类型告警）
     *
     * @param pageNumber 页码
     * @return 车辆配置列表
     */
    private List<RiskAlarmConfigData> queryVehicleConfigurationsBatch(int pageNumber) {
        try {
            return tableRiskAlarmConfigService.selectList(
                    QueryRiskAlarmConfigReq.newBuilder()
                            .setAlarmType(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())
                            .setPageNum(pageNumber)
                            .setPageSize(PAGE_SIZE)
                            .build()
            );
        } catch (Exception e) {
            log.error("查询第{}页车辆配置失败", pageNumber, e);
            throw new RuntimeException("查询车辆配置失败", e);
        }
    }

    /**
     * 处理车辆批次
     *
     * @param vehicleConfigurations 车辆配置列表
     * @return 批次处理结果
     */
    private void processVehicleBatch(List<RiskAlarmConfigData> vehicleConfigurations, Integer alarmType) {
        if (CollectionUtil.isEmpty(vehicleConfigurations)) {
            return;
        }

        List<String> vinList = vehicleConfigurations.stream().map(RiskAlarmConfigData::getVin).collect(Collectors.toList());

        riskAlarmConfigService.addRiskAlarmConfig(AddRiskAlarmConfiReq.newBuilder().addAllVinList(vinList).setAlarmType(alarmType).build());
    }
}

