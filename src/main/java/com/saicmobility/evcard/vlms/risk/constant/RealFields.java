package com.saicmobility.evcard.vlms.risk.constant;

/**
 * 车辆实时数据的字段名称.<br>
 * <AUTHOR>
 *
 */
public class RealFields {

	/**
	 * 车机GPS位置信息key值
	 */
	public static final String VEHICLE_REALDATA_KEY = "vehRealData_";

	/**
	 * 第三方GPS位置信息key值
	 */
	public static final String VEHICLE_THIRD_REALDATA_KEY = "WANWEIGPS_VIN_";
	
	/**
	 * 终端最后一次上报数据的时间.<br>
	 */
	public static final String UpdateTime = "UpdateTime";
	
	/**
	 * acc开关
	 */
	public static final String accFlag = "ACCFLG";
	
	/**
	 * 小电瓶电压.<br>
	 */
	public static final String BCMVolt = "BCMVolt";
	
	/**
	 * 快充插头插入
	 */
	public static final String BMSFastChgPlugOn = "BMSFastChgPlugOn";
	
	/**
	 * 慢充插头插入
	 */
	public static final String BMSSlowChgPlugOn = "BMSSlowChgPlugOn";
	
	/**
	 * 充电状态
	 */
	public static final String ChargeStatus = "ChargeStatus";
	
	/**
	 * soc电池电量.
	 */
	public static final String BMSPackSOCDisplay = "BMSPackSOCDisplay";
	
	/**
	 * 后备箱打开
	 */
	public static final String BonnetOpen = "BonnetOpen";
	
	/**
	 * 后备箱解锁/加锁
	 */
	public static final String BonnetStatus = "BonnetStatus";
	
	/**
	 * CAN通信异常
	 */
	public static final String CanCommunicationError = "CanCommunicationError";
	
	/**
	 * 失能钥匙异常
	 */
	public static final String CanDiableKeyError = "CanDiableKeyError";
	
	/**
	 * 使能钥匙异常
	 */
	public static final String CanEnableKeyError = "CanEnableKeyError";
	
	/**
	 * 车门解锁.加锁
	 */
	public static final String CarDoorStatus = "CarDoorStatus";
	
	/**
	 * 电子设备状态
	 */
	public static final String DevicesStatus = "DevicesStatus";
	
	/**
	 * 方向
	 */
	public static final String Direction = "Direction";
	
	/**
	 * 主驾驶门打开
	 */
	public static final String DriverDoorOpen = "DriverDoorOpen";
	
	/**
	 * 主驾驶门解锁/加锁
	 */
	public static final String DriverDoorStatus = "DriverDoorStatus";
	
	/**
	 * 副驾驶门打开
	 */
	public static final String PassDoorOpen = "PassDoorOpen";
	
	/**
	 * 副驾驶门加锁/解锁
	 */
	public static final String PassDoorStatus = "PassDoorStatus";
	
	/**
	 * 后主驾驶门打开
	 */
	public static final String DriverAfterDoorOpen = "DriverAfterDoorOpen";
	
	/**
	 * 后主驾驶门解锁/加锁
	 */
	public static final String DriverAfterDoorStatus = "DriverAfterDoorStatus";
	
	/**
	 * 后副驾驶门打开
	 */
	public static final String PassAfterDoorOpen = "PassAfterDoorOpen";
	
	/**
	 * 后副驾驶门解锁/加锁
	 */
	public static final String PassAfterDoorStatus = "PassAfterDoorStatus";
	
	/**
	 * 车辆电路正常.断开
	 */
	public static final String ElectricCircuit = "ElectricCircuit";
	
	/**
	 * 最新一次定位是否有效.<br>
	 */
	public static final String FixedPosition = "FixedPosition";
	
	/**
	 * 点火状态
	 */
	public static final String IgniteStates = "IgniteStates";
	
	/**
	 * 纬度（int类型）
	 */
	public static final String Latitude = "Latitude";
	
	/**
	 * 纬度类型
	 */
	public static final String LatitudeFlg = "LatitudeFlg";
	
	/**
	 * 经度（int类型）
	 */
	public static final String Longitude = "Longitude";
	
	/**
	 * 经度类型
	 */
	public static final String LongitudeFlg = "LongitudeFlg";
	
	/**
	 * 锁车状态
	 */
	public static final String LockStatus = "LockStatus";
	
	/**
	 * 最近ACC点火时间
	 */
	public static final String LstAccONTime = "LstAccONTime";
	
	/**
	 * 累计里程
	 */
	public static final String Mileage = "Mileage";
	/**
	 * 累计里程的上传时间.
	 */
	public static final String Mileage_Time = "Mileage_Time";
	/**
	 * 取车时车辆的总里程.<br>
	 */
	public static final String getMileage = "getMileage";
	
	/**
	 * obd里程.<br>
	 */
	public static final String obdMileage = "obdMileage";
	
	/**
	 * 最后一次上报的里程.<br>
	 */
	public static final String latestMileage = "latestMileage";
	
	/**
	 * 上下线
	 */
	@Deprecated
	public static final String Online = "Online";
	
	/**
	 * 上电
	 */
	public static final String PowerOn = "PowerOn";
	
	/**
	 * 档位位置
	 */
	public static final String ShifterPosition = "ShifterPosition";
	
	/**
	 * 边灯开
	 */
	public static final String SideLampOn = "SideLampOn";
	
	/**
	 * 速度
	 */
	public static final String Speed = "Speed";
	
	/**
	 * GPS速度
	 */
	public static final String GPSSpeed = "GPSSpeed";
	
	/**
	 * can数据时间撮 长int型
	 */
	public static final String canDateTime = "canDateTime";
	
	/**
	 * gps数据时间撮 长int型
	 */
	public static final String gpsDateTime = "gpsDateTime";
	
	/**
	 * 油量百分比
	 */
	public static final String FuelSOC = "FuelSOC";
	/**
	 * 油量(L)
	 */
	public static final String oil = "oil";
	
	/**
	 * PT准备指示.<br>
	 */
	public static final String PTReadyInd = "PTReadyInd";
	
	/**
	 * 系统模式(0:Off 1:ACC 2:Run 3:Crank)
	 */
	public static final String SysPowerMode = "SysPowerMode";
	
	/**
	 * 终端租赁模式 0 分时 1 长租
	 */
	public static String rentMode = "rentMode";
	
	/**
	 * 换算地理位置的时间.<br>
	 */
	public static String gpsAddressTime = "gpsAddressTime";
	
	/**
	 * 车辆位置.格式为JSON<br>
	 */
	public static String gpsAddress = "gpsAddress";
	
	public static String locationReqId = "locationReqId";
	
	public static String locationBfTime = "locationBfTime";
	/**
	 * 每1%的soc可以行驶的里程.<br>
	 */
	public static String enduranceAvg = "enduranceAvg";
	/**
	 * 总续航里程(电量续航 + 油量续航)
	 */
	public static String endurance = "endurance";
	
	/**
	 * 油量续航里程
	 */
	public static String oilEndurance = "oilEndurance";
	
	/**
	 * 续航里程时间.
	 */
	public static final String enduranceTime = "enduranceTime";

	/**
	 * 定位精度
	 */
	public static final String ACCURACY = "accuracy";

	/**	 * 高度
	 */
	public static final String HEIGHT = "height";

	/**
	 * 终端平台 1、万位科技 2、紫米科技 3、途骏科技
	 */
	public static final String TBOX_TYPE = "tboxType";


	public static final String TERM_STATIC_KEY = "termStaticInfo_";

	public static String ALARM_VEHICLE = "ALARM_";
	/**
	 * 数据来源
	 */
	public static String dataSource = "dataSource";

	/**
	 * 车辆超电子围栏
	 */
	public static final String VEHICLE_ELECTRON_KEY = "vehicle_electron_key_";

	/**
	 * 车辆进入高危区域
	 */
	public static final String VEHICLE_HIGH_RISK_KEY = "vehicle_high_risk_key_";

}
