package com.saicmobility.evcard.vlms.risk.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.saicmobility.evcard.utils.FileUtils;
import com.saicmobility.evcard.utils.LocalDateUtil;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskWhitelistConfigService;
import com.saicmobility.evcard.vlms.risk.dto.whitelist.RiskWhitelistConfigData;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.error.ResultCode;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.excel.ExportErrorInfo;
import com.saicmobility.evcard.vlms.risk.excel.ImportVehicleDemo;
import com.saicmobility.evcard.vlms.risk.model.Attachment;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.model.RiskWhitelistConfig;
import com.saicmobility.evcard.vlms.risk.service.ExportFileService;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.service.RiskCommonService;
import com.saicmobility.evcard.vlms.risk.service.RiskWhitelistConfigService;
import com.saicmobility.evcard.vlms.risk.service.base.BaseService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubRiskAlaramService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.ExcelUtil;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.AssetsVehicle;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;
import jodd.util.StringUtil;
import krpc.rpc.util.BeanToMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
@Service
@Slf4j
public class RiskWhitelistConfigServiceImpl extends BaseService implements RiskWhitelistConfigService{

    @Resource
    private TableRiskWhitelistConfigService whitelistConfigService;

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    private ExportFileService exportFileService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private RiskCommonService riskCommonService;

    @Resource
    private SubAssetsService subAssetsService;

    @Resource
    private RiskAlarmConfigService riskAlarmConfigService;

    @Resource
    private SubRiskAlaramService subRiskAlaramService;

    private static final List<Integer> ALARM_TYPE_LIST = Arrays.asList(
            AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode(),
            AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode(),
            AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(),
            AlarmTypeEnum.VEHICLE_MOVEMENT.getCode(),
            AlarmTypeEnum.INSURANCE_ADVENT.getCode(),
            AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(),
            AlarmTypeEnum.HIGH_RISK_FENCE.getCode()
    );

    @Override
    public QueryWhiteListRes queryWhiteList(QueryWhiteListReq req) {
        QueryWhiteListRes.Builder builder = QueryWhiteListRes.newBuilder().setTotal(whitelistConfigService.selectTotal(req));
        List<RiskWhitelistConfigData> riskWhitelistConfigs = whitelistConfigService.selectList(req);
        for (RiskWhitelistConfigData riskWhitelistConfig : riskWhitelistConfigs) {
            riskWhitelistConfig.setOperateOrgName(Global.instance.configLoader.getOrgName(riskWhitelistConfig.getOperateOrgId()));
            riskWhitelistConfig.setOrgName(Global.instance.configLoader.getOrgName(riskWhitelistConfig.getOrgId()));
            // 查询附件
            List<Attachment> attachmentList = queryAttachment(4, String.valueOf(riskWhitelistConfig.getId()));
            if (CollectionUtil.isNotEmpty(attachmentList)){
                riskWhitelistConfig.setAttachmentPaths(attachmentList);
            }
            WhiteListConfig whiteListConfig = BeanToMessage.toMessage(WhiteListConfig.class, riskWhitelistConfig);
            builder.addList(whiteListConfig);
        }
        return builder.build();
    }

    @Override
    public ImportVehicleRes importVehicles(ImportVehicleReq req) {
        // 解析excel
        List<ImportVehicleDemo> vehicleDemoList = ExcelUtil.read(req.getFilePath(), 0, ImportVehicleDemo.class);
        if (CollectionUtil.isEmpty(vehicleDemoList)) {
            throw new ServiceException("请上传车辆清单");
        }
        if (vehicleDemoList.size() > 5000){
           throw new ServiceException("单次上传限制5000条");
        }

        // 附件
        List<AttachmentInfo> attachmentPathsList = req.getAttachmentPathsList();
        if (CollectionUtil.isEmpty(attachmentPathsList)){
            throw new ServiceException("请上传附件");
        }
        CurrentUser currentUser = req.getCurrentUser();
        List<RiskWhitelistConfig> whiteListConfigList = new ArrayList<>();
        List<ExportErrorInfo> errorInfoList = new ArrayList<>();
        Map<String, Integer> checkMap = new LinkedHashMap<>();
        int row = 1;

        for (ImportVehicleDemo importVehicleDemo : vehicleDemoList) {
            String vin = importVehicleDemo.getVin();
            // 车架号
            if (StringUtil.isBlank(vin)) {
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行车架号为空", row)));
                continue;
            }
            // 判断车辆是否存在
            AssetsVehicle assetsVehicle = getVehicleInfoByVin(vin);
            if (assetsVehicle == null){
                errorInfoList.add(createErrorInfo(vin, "车辆不存在"));
            }else {
                if (ObjectUtil.isNotEmpty(checkMap.get(vin)) && checkMap.get(vin) == AlarmTypeEnum.getCodeByValue(importVehicleDemo.getAlarmType())) {
                    errorInfoList.add(createErrorInfo(vin, "存在重复报警类型的白名单配置，请检查上传清单"));
                }
                // 报警类型
                Integer alarmType = null;
                if (StringUtils.isBlank(importVehicleDemo.getAlarmType())){
                    errorInfoList.add(createErrorInfo(vin, "报警类型不能为空"));
                }else {
                    alarmType = AlarmTypeEnum.getCodeByValue(importVehicleDemo.getAlarmType());
                    if (alarmType == null || !ALARM_TYPE_LIST.contains(alarmType)){
                        errorInfoList.add(createErrorInfo(vin, "报警类型必须是六种类型中的一种"));
                    }
                }

                // 有效截止日期
                String expirationDate = importVehicleDemo.getExpirationDate();
                if (StringUtil.isBlank(expirationDate)){
                    errorInfoList.add(createErrorInfo(vin, "有效截止日期不能为空"));
                }
                // 日期处理
                Date dateFromExpirationDate = null;
                Integer effectiveStatus = null;
                try {
                    dateFromExpirationDate = DateUtil.getDateFromDateStr(expirationDate + " 00:00:00", DateUtil.DATE_TYPE1);
                    effectiveStatus = EffectiveStatusEnum.EFFECTIVE_STATUS.getCode();
                    // 如果时间小于当前时间则设置为已失效 大于则设置为生效中
                    if (dateFromExpirationDate.compareTo(new Date()) <= 0){
                        effectiveStatus = EffectiveStatusEnum.EFFECTIVE_RECOVER.getCode();
                    }
                }catch (Exception e){
                    errorInfoList.add(createErrorInfo(vin, "有效截止日期格式错误"));
                }
                // 判断类型是否存在
                RiskWhitelistConfig riskWhitelistConfig = new RiskWhitelistConfig();
                riskWhitelistConfig.setVin(vin);
                riskWhitelistConfig.setAlarmType(alarmType);
                riskWhitelistConfig.setEffectiveStatus(EffectiveStatusEnum.EFFECTIVE_STATUS.getCode());
                RiskWhitelistConfig whitelistConfig = whitelistConfigService.selectConfig(riskWhitelistConfig);
                if (whitelistConfig != null){
                    errorInfoList.add(createErrorInfo(vin, StrUtil.format("报警类型:【{}】存在生效中的白名单", importVehicleDemo.getAlarmType())));
                }
                // 构建白名单配置
                whiteListConfigList.add(buidWhiteListConfig(assetsVehicle, alarmType, dateFromExpirationDate, effectiveStatus, currentUser,importVehicleDemo.getMiscDesc()));
            }
            row ++;
        }
        if (CollectionUtil.isNotEmpty(errorInfoList)){
            String fileName = CharSequenceUtil.format("添加风险报警白名单上传错误_{}.xlsx", cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
            asynchronousUpload(fileName,115,errorInfoList, req.getCurrentUser());
            throw new ServiceException("导入错误：请去文件导出页面，下载导入错误信息");
        }
        // 新增数据
        for (RiskWhitelistConfig riskWhitelistConfig : whiteListConfigList) {
            whitelistConfigService.insert(riskWhitelistConfig);
            // 如果有报警中的风险，则立即解除
            doAlarmRisk(riskWhitelistConfig.getVin(), riskWhitelistConfig.getAlarmType(), riskWhitelistConfig.getExpirationDate(),currentUser);

            // 日志
            String logContent = StrUtil.format("新增风控报警白名单：报警类型：【{}】有效期：【{}】", AlarmTypeEnum.getValueByCode(riskWhitelistConfig.getAlarmType()), DateUtil.format(riskWhitelistConfig.getExpirationDate(), "yyyy-MM-dd"));
            saveVehicleLog(riskWhitelistConfig.getVin(), logContent, req.getCurrentUser());
            saveOperateLog(logContent, riskWhitelistConfig.getId()+"", OperateTypeEnum.OPERATE_WHITE_LIST_CONFIG.getCode(), req.getCurrentUser());

            batchUploadAttachment(attachmentPathsList,4,String.valueOf(riskWhitelistConfig.getId()),req.getCurrentUser());
        }
        return ImportVehicleRes.ok();
    }

    /**
     * 报警风险逻辑
     * @param vin
     * @param alarmType
     * @param currentUser
     * @param expirationDate
     */
    private void doAlarmRisk(String vin, Integer alarmType, Date expirationDate, CurrentUser currentUser) {
        // 查询是否有进行中的报警
        RiskAlarm riskAlarm = tableRiskAlarmService.queryDataByVinAndType(vin, alarmType);
        if (riskAlarm == null){
            return;
        }
        // 如果过期时间大于等于当前时间，则将报警状态设置为已恢复
        if (expirationDate.after(new Date())){

            riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
            riskAlarm.setRecoverMethod(RecoverMethodEnum.AUTO_RECOVER.getCode());
            riskAlarm.setRecoverChannel(AlarmSystemEnum.WTXT.getCode());
            riskAlarm.setRecoveryDate(new Date());
            riskAlarm.setUpdateOperName(currentUser.getNickName());
            riskAlarm.setUpdateOperAccount(currentUser.getUserAccount());
            tableRiskAlarmService.update(riskAlarm);

            int maxVehicleStatus = riskCommonService.queryMaxVehicleRiskStatus(riskAlarm.getVin(), null);
            subAssetsService.updateVehicleRiskStatusByVin(riskAlarm.getVin(), maxVehicleStatus);

            // 操作日志
            saveOperateLog("新增白名单配置,解除报警", riskAlarm.getAlarmNo(),OperateTypeEnum.OPERATE_TYPE.getCode(), currentUser);
            // 车辆日志
            saveVehicleLog(riskAlarm.getVin(), "新增白名单配置,解除报警", currentUser);

            // 报警配置修改
            Long aLong = riskAlarmConfigService.updateRiskAlarmConfig(riskAlarm.getVin(), riskAlarm.getAlarmType(), new Date(), null, null, currentUser);
            saveOperateLog("新增白名单配置, 解除报警， 更新报警开始时间", aLong+"", OperateTypeEnum.OPERATE_CONFIG.getCode(), currentUser);

            // 通知长租
            subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
        }
    }

    /**
     * 构建白名单配置
     * @param assetsVehicle
     * @param alarmType
     * @param expirationDate
     * @param currentUser
     * @param effectiveStatus
     * @return
     */
    private RiskWhitelistConfig buidWhiteListConfig(AssetsVehicle assetsVehicle,
                                                    Integer alarmType, Date expirationDate,
                                                    Integer effectiveStatus, CurrentUser currentUser,String miscDesc){
        RiskWhitelistConfig riskWhitelistConfig = new RiskWhitelistConfig();
        riskWhitelistConfig.setVin(assetsVehicle.getVin());
        //riskWhitelistConfig.setPlateNo(assetsVehicle.getPlateNo());
        riskWhitelistConfig.setAlarmType(alarmType);
        riskWhitelistConfig.setEffectiveStatus(effectiveStatus);
        riskWhitelistConfig.setExpirationDate(expirationDate);
        /*riskWhitelistConfig.setOrgId(assetsVehicle.getPropertyOrgId());
        riskWhitelistConfig.setOrgName(assetsVehicle.getPropertyOrgName());
        riskWhitelistConfig.setOperateOrgId(assetsVehicle.getOperationOrgId());
        riskWhitelistConfig.setOperateOrgName(assetsVehicle.getOperationOrgName());*/
        riskWhitelistConfig.setCreateOperName(currentUser.getNickName());
        riskWhitelistConfig.setCreateOperAccount(currentUser.getUserAccount());
        riskWhitelistConfig.setCreateTime(new Date());
        riskWhitelistConfig.setMiscDesc(miscDesc);
        return riskWhitelistConfig;
    }

    @Override
    public UpdateWhiteListConfigRes updateWhiteListConfig(UpdateWhiteListConfigReq req) {
        RiskWhitelistConfig riskWhitelistConfig = whitelistConfigService.selectById(req.getId());
        if (riskWhitelistConfig == null){
            throw new ServiceException("白名单不存在");
        }
        if (riskWhitelistConfig.getEffectiveStatus().equals(EffectiveStatusEnum.EFFECTIVE_RECOVER.getCode())){
            throw new ServiceException("白名单已过期，不能修改");
        }
        Date dateFromDate = DateUtil.getDateFromDateStr(req.getExpirationDate() + " 00:00:00", DateUtil.DATE_TYPE1);
        riskWhitelistConfig.setExpirationDate(dateFromDate);
        if (dateFromDate.compareTo(new Date()) <= 0){
            riskWhitelistConfig.setEffectiveStatus(EffectiveStatusEnum.EFFECTIVE_RECOVER.getCode());
        }
        // 更新
        whitelistConfigService.updateById(riskWhitelistConfig);

        // 日志
        String logContent = StrUtil.format("修改风控报警白名单：报警类型：【{}】有效期：【{}】", AlarmTypeEnum.getValueByCode(riskWhitelistConfig.getAlarmType()), DateUtil.format(riskWhitelistConfig.getExpirationDate(), "yyyy-MM-dd"));
        saveVehicleLog(riskWhitelistConfig.getVin(), logContent, req.getCurrentUser());
        saveOperateLog(logContent, riskWhitelistConfig.getId()+"", OperateTypeEnum.OPERATE_WHITE_LIST_CONFIG.getCode(), req.getCurrentUser());

        // 附件
        List<AttachmentInfo> attachmentPathsList = req.getAttachmentPathsList();
        if (CollectionUtil.isNotEmpty(attachmentPathsList)){
            // 先删除历史附件
            deleteAttachmentByFileType(4,String.valueOf(riskWhitelistConfig.getId()));
            // 上传新的附件
            batchUploadAttachment(attachmentPathsList,4, String.valueOf(riskWhitelistConfig.getId()),req.getCurrentUser());
        }
        return UpdateWhiteListConfigRes.ok();
    }

    @Override
    public QueryWhiteListRes exportWhiteList(QueryWhiteListReq req) {
        long total = whitelistConfigService.selectTotal(req);
        if (total == 0){
            throw new ServiceException(ResultCode.Fail, "没有符合条件的白名单不能导出");
        }
        CurrentUser currentUser = req.getCurrentUser();
        //文件根路径
        String mfsRootPath = Global.instance.getMfsRootPath();
        //文件下载地址
        String projectDownloadUrl = Global.instance.getProjectDownloadUrl();

        String fileName = ExcelTypeEnum.EXCEL_TYPE_4.getValue()+"_"+ LocalDateUtil.format(new Date(), LocalDateUtil.UNSIGNED_DATETIME_PATTERN) +".xlsx";
        String templateFile = "template/"+ExcelTypeEnum.EXCEL_TYPE_4.getTemplate()+".xlsx";

        // 开始导出
        String fileName1 = ExcelTypeEnum.EXCEL_TYPE_4.getValue();
        String exportFilePath = FileUtils.getFilePathSuffix(fileName1,1);
        String fullExportFilePath = mfsRootPath + exportFilePath;
        String downLoadFilePath = projectDownloadUrl + exportFilePath;

        Integer exportFileId = exportFileService.startExport(FileSourceEnum.FILE_SOURCE_19.getSource(), fileName, downLoadFilePath, currentUser.getNickName(),
                currentUser.getUserAccount(), currentUser.getOrgId(), currentUser.getOrgName());
        if (exportFileId == null){
            throw new ServiceException("创建导出文件失败");
        }

        List<RiskWhitelistConfigData> whitelistConfigDataList = new ArrayList<>();
        // 异步导出
        taskExecutor.execute(()->{
            // 每页条数
            int pageSize = 5000;
            int totalPageNum = ((int)total + pageSize - 1) / pageSize;
            int pageNum = 1;

            do {
                QueryWhiteListReq.Builder builder = req.toBuilder();
                builder.setPageNum(pageNum).setPageSize(pageSize);
                List<RiskWhitelistConfigData> riskWhitelistConfigs = whitelistConfigService.selectList(builder.build());
                if (CollectionUtil.isEmpty(riskWhitelistConfigs)){
                    break;
                }
                for (RiskWhitelistConfigData riskWhitelistConfigData : riskWhitelistConfigs) {

                    riskWhitelistConfigData.setOperateOrgName(Global.instance.configLoader.getOrgName(riskWhitelistConfigData.getOperateOrgId()));
                    riskWhitelistConfigData.setOrgName(Global.instance.configLoader.getOrgName(riskWhitelistConfigData.getOrgId()));
                    riskWhitelistConfigData.setAlarmTypeDesc(AlarmTypeEnum.getValueByCode(riskWhitelistConfigData.getAlarmType()));
                    riskWhitelistConfigData.setExpirationDateStr(DateUtil.dateToString(riskWhitelistConfigData.getExpirationDate(), DateUtil.DATE_TYPE5));
                    riskWhitelistConfigData.setCreateTimeStr(DateUtil.dateToString(riskWhitelistConfigData.getCreateTime(), DateUtil.DATE_TYPE1));
                    riskWhitelistConfigData.setEffectiveStatusDesc(EffectiveStatusEnum.getValueByCode(riskWhitelistConfigData.getEffectiveStatus()));
                }
                whitelistConfigDataList.addAll(riskWhitelistConfigs);
                pageNum ++;
            }while (totalPageNum >= pageNum);

            //如果目录不存在，则创建目录
            File file = new File(fullExportFilePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            boolean noResult = ObjectUtil.isNull(whitelistConfigDataList);
            try (
                    InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream(templateFile);
                    FileOutputStream fileOutputStream = new FileOutputStream(fullExportFilePath);
                    ExcelWriter excelWriter = EasyExcelFactory.write(fileOutputStream).withTemplate(templateStream).build();

            ) {
                WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                //无数据处理
                if(noResult){
                    excelWriter.fill(new ArrayList<>(), writeSheet);
                }else {
                    excelWriter.fill(whitelistConfigDataList, writeSheet);
                }
                fileOutputStream.flush();
                excelWriter.finish();
                //导出成功
                exportFileService.exportSuccess(exportFileId, currentUser.getNickName());
            }catch (Exception e) {
                e.printStackTrace();
                log.error(e.getLocalizedMessage(), e);
                //导出失败
                exportFileService.exportFail(exportFileId, e.getLocalizedMessage(), currentUser.getNickName());
            }
        });
        return QueryWhiteListRes.ok();
    }

    @Override
    public CloseWhiteListConfigRes closeWhiteListConfig(CloseWhiteListConfigReq req) {
        RiskWhitelistConfig riskWhitelistConfig = whitelistConfigService.selectById(req.getId());
        if (riskWhitelistConfig == null){
            throw new ServiceException("白名单不存在");
        }
        riskWhitelistConfig.setEffectiveStatus(EffectiveStatusEnum.EFFECTIVE_RECOVER.getCode());
        // 更新
        whitelistConfigService.updateById(riskWhitelistConfig);

        // 日志
        String logContent = StrUtil.format("关闭风控报警白名单：报警类型：【{}】", AlarmTypeEnum.getValueByCode(riskWhitelistConfig.getAlarmType()));
        saveVehicleLog(riskWhitelistConfig.getVin(), logContent, req.getCurrentUser());
        saveOperateLog(logContent, riskWhitelistConfig.getId()+"", OperateTypeEnum.OPERATE_WHITE_LIST_CONFIG.getCode(), req.getCurrentUser());
        return CloseWhiteListConfigRes.ok();
    }

   /* public AssetsVehicle getVehicleInfoByVin(String vin){
        GetAssetsVehicleByVinRes res = vlmsAssetsService.getAssetsVehicleByVin(GetAssetsVehicleByVinReq.newBuilder().addVin(vin).build());
        List<AssetsVehicle> vehicles = res.getInfoList();
        if (CollectionUtil.isEmpty(vehicles)) {
            return null;
        }
        return vehicles.get(0);
    }*/
}
