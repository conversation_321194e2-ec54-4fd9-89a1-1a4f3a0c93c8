package com.saicmobility.evcard.vlms.risk.database.impl;

import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.dto.riskAlarmConfig.RiskAlarmConfigData;
import com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmConfigMapper;
import com.saicmobility.evcard.vlms.risk.mapper.extend.RiskAlarmConfigExtendMapper;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import com.saicmobility.evcard.vlms.risk.service.base.BaseService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskAlarmConfigReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmConfigDynamicSqlSupport.riskAlarmConfig;
import static com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmDynamicSqlSupport.riskAlarm;
import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.AssetsVehicleDynamicSqlSupport.assetsVehicle;
import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.VehicleLicenseInfoDynamicSqlSupport.vehicleLicenseInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-01-17
 */
@Service
@Slf4j
public class TableRiskAlarmConfigServiceImpl extends BaseService implements TableRiskAlarmConfigService {

    @Resource
    public RiskAlarmConfigMapper riskAlarmConfigMapper;

    @Resource
    public RiskAlarmConfigExtendMapper riskAlarmConfigExtendMapper;

    @Override
    public List<RiskAlarmConfigData> selectList(QueryRiskAlarmConfigReq req) {

        String userOrgId = req.getCurrentUser().getOrgId();
        int pageNum = req.getPageNum() == 0 ? 1 : req.getPageNum();
        int pageSize = req.getPageSize() == 0 ? 10 : req.getPageSize();
        int limitNum = (pageNum -1) * pageSize;
        // 条件查询
        SelectStatementProvider render = select(
                riskAlarmConfig.id,
                riskAlarmConfig.vin,
                riskAlarmConfig.alarmType,
                riskAlarmConfig.pauseDay,
                riskAlarmConfig.alarmStartTime,
                riskAlarmConfig.pauseDeadlineTime,
                assetsVehicle.propertyOrgId,
                assetsVehicle.propertyOrgName,
                assetsVehicle.operationOrgId,
                assetsVehicle.operationOrgName,
                vehicleLicenseInfo.plateNo
        )
                .from(riskAlarmConfig)
                .leftJoin(assetsVehicle).on(assetsVehicle.vin, equalTo(riskAlarmConfig.vin))
                .leftJoin(vehicleLicenseInfo).on(vehicleLicenseInfo.vin, equalTo(riskAlarmConfig.vin))
                .where()
                .and(riskAlarmConfig.vin, isEqualToWhenPresent(req.getVin()).filter(StringUtils::isNotBlank))
                .and(vehicleLicenseInfo.plateNo, isEqualToWhenPresent(req.getPlateNo()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualToWhenPresent(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId)))
                .and(riskAlarmConfig.alarmType, isEqualToWhenPresent(req.getAlarmType()).filter(v-> v!= 0))
                .and(riskAlarmConfig.isDeleted, isEqualTo(0))
                .orderBy(riskAlarmConfig.id.descending())
                .limit(pageSize)
                .offset(limitNum)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskAlarmConfigExtendMapper.selectAlarmConfigList(render);
    }

    @Override
    public long selectTotal(QueryRiskAlarmConfigReq req) {
        String userOrgId = req.getCurrentUser().getOrgId();
        // 条件查询
        SelectStatementProvider render = select(
                count(riskAlarmConfig.id)
        )
                .from(riskAlarmConfig)
                .leftJoin(assetsVehicle).on(assetsVehicle.vin, equalTo(riskAlarmConfig.vin))
                .leftJoin(vehicleLicenseInfo).on(vehicleLicenseInfo.vin, equalTo(riskAlarmConfig.vin))
                .where()
                .and(riskAlarmConfig.vin, isEqualToWhenPresent(req.getVin()).filter(StringUtils::isNotBlank))
                .and(vehicleLicenseInfo.plateNo, isEqualToWhenPresent(req.getPlateNo()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualToWhenPresent(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId)))
                .and(riskAlarmConfig.alarmType, isEqualToWhenPresent(req.getAlarmType()).filter(v-> v!= 0))
                .and(riskAlarmConfig.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskAlarmConfigMapper.count(render);
    }

    @Override
    public void updateById(RiskAlarmConfig riskAlarmConfig) {
        riskAlarmConfigMapper.updateByPrimaryKeySelective(riskAlarmConfig);
    }

    @Override
    public RiskAlarmConfig selectById(Long id) {
        return riskAlarmConfigMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public void updateRiskAlarmConfig(RiskAlarmConfig alarmConfig) {
        UpdateStatementProvider updateProvider =  SqlBuilder.update(riskAlarmConfig)
                .set(riskAlarmConfig.alarmStartTime).equalToWhenPresent(alarmConfig.getAlarmStartTime())
                .set(riskAlarmConfig.pauseDay).equalToWhenPresent(alarmConfig.getPauseDay())
                .set(riskAlarmConfig.pauseDeadlineTime).equalToWhenPresent(alarmConfig.getPauseDeadlineTime())
                .set(riskAlarmConfig.updateOperAccount).equalToWhenPresent(alarmConfig.getUpdateOperAccount())
                .set(riskAlarmConfig.updateOperName).equalToWhenPresent(alarmConfig.getUpdateOperName())
                .where(riskAlarmConfig.vin, isEqualTo(alarmConfig.getVin()))
                .and(riskAlarmConfig.alarmType, isEqualTo(alarmConfig.getAlarmType()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        riskAlarmConfigMapper.update(updateProvider);
    }

    @Override
    public RiskAlarmConfig selectRiskAlarmConfig(RiskAlarmConfig config) {
        return riskAlarmConfigMapper.selectOne(buildQuery(config)).orElse(null);
    }

    @Override
    public void updateByVin(String vin) {
        UpdateStatementProvider updateProvider =  SqlBuilder.update(riskAlarmConfig)
                .set(riskAlarmConfig.alarmStartTime).equalTo(new Date())
                .set(riskAlarmConfig.updateOperName).equalTo("定时任务自动")
                .where(riskAlarmConfig.vin, isEqualTo(vin))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        riskAlarmConfigMapper.update(updateProvider);
    }

    @Override
    public RiskAlarmConfig selectRiskAlarmConfig(String vin, Integer alarmType) {
        RiskAlarmConfig riskAlarmConfig = new RiskAlarmConfig();
        riskAlarmConfig.setVin(vin);
        riskAlarmConfig.setAlarmType(alarmType);
        return riskAlarmConfigMapper.selectOne(buildQuery(riskAlarmConfig)).orElse(null);
    }

    @Override
    public void insert(RiskAlarmConfig riskAlarmConfig) {
        riskAlarmConfigMapper.insertSelective(riskAlarmConfig);
    }

    @Override
    public List<RiskAlarmConfig> selectConfigGroupByVin(int pageNum, Integer pageSize) {
        int limitNum = (pageNum -1) * pageSize;
        // 条件查询
        SelectStatementProvider render = select(
                riskAlarmConfig.vin
        )
                .from(riskAlarmConfig)
                .where()
                .and(riskAlarmConfig.isDeleted, isEqualTo(0))
                .limit(pageSize)
                .offset(limitNum)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskAlarmConfigExtendMapper.selectMany(render);
    }

    @Override
    public void deleteByVinList(List<String> deleteVinList) {
        DeleteStatementProvider render = deleteFrom(riskAlarmConfig)
                .where(riskAlarmConfig.vin, isIn(deleteVinList))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        riskAlarmConfigMapper.delete(render);
    }

    public SelectStatementProvider buildQuery(RiskAlarmConfig config){
        return select(
                riskAlarmConfig.allColumns())
                .from(riskAlarmConfig)
                .where()
                .and(riskAlarmConfig.vin, isEqualToWhenPresent(config.getVin()))
                .and(riskAlarmConfig.alarmType, isEqualToWhenPresent(config.getAlarmType()))
                .and(riskAlarm.isDeleted, isEqualTo(0))
                .orderBy(riskAlarmConfig.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
    }
}
