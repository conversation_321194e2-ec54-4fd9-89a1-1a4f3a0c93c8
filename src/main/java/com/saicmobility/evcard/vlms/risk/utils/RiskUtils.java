package com.saicmobility.evcard.vlms.risk.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.enums.AlarmTypeEnum;
import com.saicmobility.evcard.vlms.risk.enums.ProductLineEnum;
import com.saicmobility.evcard.vlms.risk.enums.PropertyStatusEnums;
import com.saicmobility.evcard.vlms.risk.enums.VehicleRiskStatusEnum;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 风控工具类
 */
@Slf4j
public class RiskUtils {

    /**
     * 梧桐风控工单同步至长租系统接口
     */
    private static final String NOTIFY_RISK_WORK_ORDER = "notifyRiskWorkOrder";

    /**
     * 梧桐关闭风控工单并同步至长租系统
     */
    private static final String NOTIFY_CANCELS_RISK_WARNING = "notifyCancelsRiskWarning";

    /**
     * 首次收车通知长租
     */
    private static final String NOTIFY_FIRST_RETURN_CAR = "riskCtrlCarFailSyncToLTRental";

    // 长租系统接口地址
    private static final String LONGRENTURL = Global.instance.getLongRentUrl();

    // 需要推送给长组的 报警类型 （电子围栏、gps离线、年检临期、保险临期、逾期未还车）
    private static final List<Integer> PUSH_LONG_RENT_ALARM = Arrays.asList(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode(), AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode(), AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(), AlarmTypeEnum.INSURANCE_ADVENT.getCode(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());

    /**
     * 不需要创建收车任务的产品线 长租 短租
     */
    private static final List<Integer> NO_CREATE_CHECK__PRODUCT_LINE = Arrays.asList(ProductLineEnum.PRODUCT_LINE_ENUM_2.getCode());

    private RiskUtils(){
    }

    /**
     * 判断是否是长租或者短租
     * @param productLine
     * @return
     */
    public static boolean isLongShortGroup(Integer productLine){
        return NO_CREATE_CHECK__PRODUCT_LINE.contains(productLine);
    }

    /**
     * 判断是否需要创建收车任务
     * @param productLine
     * @param alarmType
     * @return
     */
    public static boolean isNeedCreateCheck(Integer productLine, Integer alarmType){
        // 产品线是否是长租或者短租
        boolean isLongRentOrShortVehicle = NO_CREATE_CHECK__PRODUCT_LINE.contains(productLine);
        // 需要推送给长组的 报警类型
        boolean isPush = PUSH_LONG_RENT_ALARM.contains(alarmType);
        if (!isLongRentOrShortVehicle || !isPush || isLongRentAndBroken(productLine, alarmType)){
            return true;
        }
        return false;
    }

    /**
     * 产品线为长租风险是车辆被扣
     * @param productLine
     * @param alarmType
     * @return
     */
    private static boolean isLongRentAndBroken(Integer productLine, Integer alarmType) {
        if (productLine == ProductLineEnum.PRODUCT_LINE_ENUM_2.getCode() && alarmType == AlarmTypeEnum.VEHICLE_IS_BROKEN.getCode()){
            return true;
        }
        return false;
    }

    /**
     * @param alarmType 梧桐报警类型 1、超出电子围栏 2、GPS信号离线 3、逾期未还车5、年检临期 6、保险临期
     * @return 返回长租的报警类型  1-车辆离线 2-电子围栏报警 5-逾期 9-年检 10 保险
     */
    public static Integer getLongRentAlarmType(Integer alarmType){
        switch (alarmType){
            case 1:
                return 2;
            case 2:
                return 1;
            case 3:
                return 5;
            case 5:
                return 9;
            case 6:
                return 10;
            case 11:
                return 11;
            default:
                return 0;
        }
    }


    /**
     * 风控报警通知长租
     * @param orderNo
     * @param plateNo
     * @param alarmLevel
     * @param alarmNo
     * @param alarmType
     * @param alarmTime
     */
    public static void notifyRiskWorkOrderToLongRent(String orderNo, String plateNo, Integer alarmLevel, String alarmNo, Integer alarmType, Integer alarmTime){
        String requestUrl = LONGRENTURL+NOTIFY_RISK_WORK_ORDER;
        HashMap<Object, Object> paramMap = new HashMap<>();
        paramMap.put("orderNo", orderNo);
        paramMap.put("vehiclePlate", plateNo);
        paramMap.put("warningType", String.valueOf(getLongRentAlarmType(alarmType)));
        paramMap.put("riskLevel", String.valueOf(alarmLevel));
        paramMap.put("riskRemark", alarmLevel+"级报警");
        paramMap.put("warningTimes", 0);
        paramMap.put("workOrderNo", alarmNo);
        log.info("notifyRiskWorkOrder 请求 长租地址:{}, 请求参数:{}", requestUrl, JSONObject.toJSONString(paramMap));

        JSONObject result = HttpClientUtils.sendRestHttp_post(requestUrl, "POST", JSONObject.toJSONString(paramMap), new HashMap<>());
        log.info("notifyRiskWorkOrder 返回结果:{}", result);
    }

    /**
     * 风控工单取消通知长租
     * @param alarmNo
     */
    public static void notifyCancelsRiskWarningToLongRent(String alarmNo){
        HashMap<Object, Object> paramMap = new HashMap<>();
        paramMap.put("workOrderNo", alarmNo);
        String requestUrl = LONGRENTURL+NOTIFY_CANCELS_RISK_WARNING;
        log.info("notifyCancelsRiskWarningToLongRent 请求 长租地址:{}, 请求参数:{}", requestUrl, JSONObject.toJSONString(paramMap));
        JSONObject result = HttpClientUtils.sendRestHttp_post(requestUrl, "POST", JSONObject.toJSONString(paramMap), new HashMap<>());
        log.info("notifyCancelsRiskWarning 返回结果:{}", result);
    }


    /**
     * 首次收车通知长租
     * @param orderNo
     */
    public static void notifyFirstReturnCarToLongRent(String orderNo) {

        HashMap<Object, Object> paramMap = new HashMap<>();
        paramMap.put("orderNo", orderNo);
        String requestUrl = LONGRENTURL+NOTIFY_FIRST_RETURN_CAR;
        log.info("riskCtrlCarFailSyncToLTRental 请求 长租地址:{}, 请求参数:{}", requestUrl, JSONObject.toJSONString(paramMap));
        JSONObject result = HttpClientUtils.sendRestHttp_post(requestUrl, "POST", JSONObject.toJSONString(paramMap), new HashMap<>());
        log.info("riskCtrlCarFailSyncToLTRental 返回结果:{}", result);
    }

    /**
     * 首次收车通知长租
     * @param orderNoParamList
     */
    public static void batchNotifyFirstReturnCarToLongRent(List<Map<String, Object>> orderNoParamList) {

        if (CollectionUtil.isEmpty(orderNoParamList)){
            return;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("riskCtrlCarFailSyncToLTRentals", orderNoParamList);
        String requestUrl = LONGRENTURL+NOTIFY_FIRST_RETURN_CAR;
        log.info("batchNotifyFirstReturnCarToLongRent riskCtrlCarFailSyncToLTRental 请求 长租地址:{}, 请求参数:{}", requestUrl, JSONObject.toJSONString(paramMap));
        JSONObject result = HttpClientUtils.sendRestHttp_post(requestUrl, "POST", JSONObject.toJSONString(paramMap), new HashMap<>());
        log.info("batchNotifyFirstReturnCarToLongRent riskCtrlCarFailSyncToLTRental 返回结果:{}", result);
    }

    /**
     * 风控收车调用车管
     * @param orderNo
     */
    public static JSONObject completelLongRentTask(String orderNo, String checkNo, String returnCarTime) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("thirdPartSeq", orderNo);
        paramMap.put("checkNo", checkNo);
        paramMap.put("operateUserName", "梧桐风控");
        paramMap.put("realReturnCarTime", returnCarTime);
        String paramStr = JSONObject.toJSONString(paramMap);
        String requestUrl = Global.instance.getBdpInnerUrl() + "innerLongRent/inner/completelLongRentTask";
        log.info("风控收车调用车管 请求车管地址:{}, 请求参数:{}", requestUrl, paramStr);
        JSONObject result = HttpClientUtils.sendRestHttp_post(requestUrl, "POST", paramStr, new HashMap<>());
        log.info("风控收车调用车管 返回结果:{}", result);
        return result;
    }

    /**
     * 风控收车调用车管
     * @param orderNo
     */
    public static JSONObject queryLongRentTask(String orderNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("thirdPartSeq", orderNo);
        paramMap.put("operateUserName", "梧桐风控");
        String paramStr = JSONObject.toJSONString(paramMap);
        String requestUrl = Global.instance.getBdpInnerUrl() + "innerLongRent/inner/queryLongRentTask";
        log.info("风控收车调用车管 请求车管地址:{}, 请求参数:{}", requestUrl, paramStr);
        JSONObject result = HttpClientUtils.sendRestHttp_post(requestUrl, "POST", paramStr, new HashMap<>());
        log.info("风控收车调用车管 返回结果:{}", result);
        return result;
    }

    public static JSONObject cancelLongRentTask(String thirdPartSeq){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("thirdPartSeq", thirdPartSeq);
        paramMap.put("taskType", 2);
        paramMap.put("operateUserName", "System");
        String paramStr = JSONObject.toJSONString(paramMap);
        String requestUrl = Global.instance.getBdpInnerUrl() + "innerLongRent/inner/cancelLongRentTask";
        log.info("取消长租收车任务 请求 车管地址:{}, 请求参数:{}", requestUrl, paramStr);
        JSONObject result = HttpClientUtils.sendRestHttp_post(requestUrl, "POST", paramStr, new HashMap<>());
        log.info("取消长租收车任务 返回结果：{}", result);
        return result;
    }


    /**
     * 根据风险等级获取车辆风险状态
     * @param isLongShortGroup
     * @param hasCollectionTask
     * @param alarmLevel
     * @return
     */
    public static Integer getRiskStatus(boolean isLongShortGroup, boolean hasCollectionTask, int alarmLevel) {
        if (isLongShortGroup) {
            if (hasCollectionTask) {
                switch (alarmLevel) {
                    case 1:
                        return VehicleRiskStatusEnum.RISK_VEHICLE.getCode();
                    case 2:
                        return VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode();
                    case 3:
                        return VehicleRiskStatusEnum.OVER_CONTROL_VEHICLE.getCode();
                    default:
                        return 0;
                }
            } else {
                if (alarmLevel == 1 || alarmLevel == 2) {
                    return VehicleRiskStatusEnum.RISK_VEHICLE.getCode();
                } else {
                    return 0;
                }
            }
        } else {
            if (hasCollectionTask) {
                switch (alarmLevel) {
                    case 1:
                        return VehicleRiskStatusEnum.RISK_VEHICLE.getCode();
                    case 2:
                        return VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode();
                    case 3:
                        return VehicleRiskStatusEnum.OVER_CONTROL_VEHICLE.getCode();
                    default:
                        return 0;
                }
            } else {
                if (alarmLevel == 1) {
                    return VehicleRiskStatusEnum.RISK_VEHICLE.getCode();
                } else {
                    return 0;
                }
            }
        }
    }

    private static final List<Integer> IGNORE_STATUSES = Arrays.asList(
            PropertyStatusEnums.BF.getCode(),
            PropertyStatusEnums.YCZ.getCode()
    );

    /**
     * 判断是否忽略告警
     * 资产状态 是 已处置、报废、已处置未过户且交付状态是已交付 不报警
     * @param propertyStatus 资产状态
     * @param deliverStatus 交付状态
     * @return
     */
    public static boolean isIgnoreAlarm(Integer propertyStatus, Integer deliverStatus){
        return IGNORE_STATUSES.contains(propertyStatus) || (PropertyStatusEnums.WGH.getCode().equals(propertyStatus) && deliverStatus == 1);
    }

}
