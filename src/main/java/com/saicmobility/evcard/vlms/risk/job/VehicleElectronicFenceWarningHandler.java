package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.saicmobility.evcard.md.mddataproxy.api.*;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.constant.RealFields;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskPushRecordService;
import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.dto.riskAlarmConfig.RiskAlarmConfigData;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.model.RiskPushRecord;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmService;
import com.saicmobility.evcard.vlms.risk.service.RiskCommonService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubRiskAlaramService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.OutDescIdUtil;
import com.saicmobility.evcard.vlms.risk.utils.RiskUtils;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileListReq;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileListRes;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.VlmsAssetsService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskAlarmConfigReq;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 车辆超出电子围栏预警
 * <AUTHOR>
 * @date 2024-02-20
 */
@Slf4j
@JobHandler(value = "vehicleElectronicFenceWarningHandler")
@Component
public class VehicleElectronicFenceWarningHandler extends IJobHandler {

    @Resource
    public OutDescIdUtil outDescIdUtil;

    @Resource
    private MdDataProxy mdDataProxy;

    @Resource
    private RiskAlarmService riskAlarmService;

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    private RiskCommonService riskCommonService;

    @Resource
    private SubAssetsService subAssetsService;

    @Resource
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;

    @Resource
    private TableOperateLogService tableOperateLogService;



    @Resource
    private SubRiskAlaramService subRiskAlaramService;


    @Resource
    private TableRiskPushRecordService tableRiskPushRecordService;


    private final static int pageSize = 1000;

    private static final Integer LONG_TERM_CODE = ParserForTypeEnum.LONG_TERM.getCode(); // 例如："LONG_TERM"
    private static final Integer SHORT_TERM_CODE = ParserForTypeEnum.SHORT_TERM.getCode(); // 例如："SHORT_TERM"
    private static final int DAYS_WARNING_3 = 3; // 3天告警
    private static final int DAYS_WARNING_7 = 7; // 7天告警
    private static final int MINUTE_WARNING_30 = 30; // 30分钟告警

    @Override
    public ReturnT<String> execute(String queryVin) throws Exception {
        log.info("vehicleElectronicFenceWarningHandler:开始执行任务!");
        Integer pageNum = 1;
        List<RiskAlarmConfigData> queryRiskAlarmConfigRes;
        do {
            queryRiskAlarmConfigRes = tableRiskAlarmConfigService.selectList(QueryRiskAlarmConfigReq.newBuilder().setVin(queryVin).setAlarmType(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())
                    .setPageNum(pageNum).setPageSize(pageSize).build());
            if (CollectionUtil.isEmpty(queryRiskAlarmConfigRes)) {
                log.info("vehicleElectronicFenceWarningHandler:未查询到风控配置超出电子围栏车辆列表!");
                return ReturnT.SUCCESS;
            }
            for(RiskAlarmConfigData vehicle : queryRiskAlarmConfigRes){
                try{
                    log.info("vin = "+vehicle.getVin());
                    if(riskCommonService.isWhiteList(vehicle.getVin(), AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())){
                        continue;
                    }
                    // 处理GPS上传时间的逻辑
                    VehicleGPSData vehicleGPSData = riskCommonService.fetchGpsDate(vehicle.getVin());
                    log.info("{}处理GPS上传时间的逻辑结果1："+ JSONUtil.toJsonStr(vehicleGPSData),vehicle.getVin());
                    if(StringUtils.isEmpty(vehicleGPSData.getCityName()) || null == vehicleGPSData.getGpsDateTime()){
                        continue;
                    }
                    SearchVehicleFileListRes searchAssetsVehicleRes = vlmsAssetsService.searchVehicleFileListForRisk(SearchVehicleFileListReq.newBuilder()
                            .setPageNum(1).setPageSize(1).setVin(vehicle.getVin()).build());
                    if(null == searchAssetsVehicleRes || searchAssetsVehicleRes.getInfoList().isEmpty()){
                        continue;
                    }
                    if(null == vehicleGPSData.getGpsDateTime() || vehicle.getAlarmStartTime().after(new Date(vehicleGPSData.getGpsDateTime()))){
                        vehicleGPSData.setGpsDateTime(vehicle.getAlarmStartTime().getTime());
                    }
                    if(null != vehicle.getPauseDeadlineTime()){
                        vehicleGPSData.setPauseDeadlineTime(vehicle.getPauseDeadlineTime());
                    }
                    log.info("{}处理GPS上传时间的逻辑结果2："+ JSONUtil.toJsonStr(vehicleGPSData),vehicle.getVin());
                    //查询是否存在进行中的异动告警
                    RiskAlarm riskAlarm = riskAlarmService.queryDataByVinAndAlarmType(vehicle.getVin(), AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode());
                    if(null == riskAlarm && null != vehicleGPSData.getPauseDeadlineTime() && vehicleGPSData.getPauseDeadlineTime().after(new Date())){
                        continue;
                    }
                    SearchVehicleFileList vehicleFileInfo = searchAssetsVehicleRes.getInfoList().get(0);
                    SearchElectronicFenceRegulationRes searchElectronicFenceRegulationRes = mdDataProxy.searchElectronicFenceRegulation(
                            SearchElectronicFenceRegulationReq.newBuilder().setVin(vehicle.getVin())
                                    .setProductLine(vehicleFileInfo.getProductLine())
                                    .setBusinessLine(vehicleFileInfo.getSubProductLine())
                                    .setOperationOrgId(vehicleFileInfo.getOperationOrgId()).build());
                    if(riskAlarm != null && riskAlarm.getAlarmStatus() == AlarmStatusEnum.ALARM_STATUS.getCode() &&
                            (searchElectronicFenceRegulationRes == null ||
                                    (searchElectronicFenceRegulationRes.getRetCode() == 0 && StringUtils.isEmpty(searchElectronicFenceRegulationRes.getPenCityScope())) ||
                                    searchElectronicFenceRegulationRes.getRetCode() == -2560888)){
                        riskCommonService.updateRiskAlarm(false,riskAlarm,vehicleGPSData,vehicleFileInfo.getProductLine());
                        continue;
                    }
                    //-2560888  未找到电子围栏规则
                    if(searchElectronicFenceRegulationRes == null || searchElectronicFenceRegulationRes.getRetCode() == -2560888 || StringUtils.isEmpty(searchElectronicFenceRegulationRes.getPenCityScope())
                            || (searchElectronicFenceRegulationRes.getPenCityScope().contains(vehicleGPSData.getCityName()) && riskAlarm == null)){
                        continue;
                    }
                    vehicleGPSData.setPenCityScope(searchElectronicFenceRegulationRes.getPenCityScope());
                    //查询当前车辆存在的进行中的最高级别告警
                    Integer vehicleRiskStatus = riskCommonService.queryMaxVehicleRiskStatus(vehicle.getVin(), null);
                    // 资产状态 0  在建工程  1  固定资产  2  固定资产（待报废）3  报废  4  固定资产(待处置)  5  固定资产(已处置) 6以租代售 7库存商品 8 已处置（未过户）
                    int propertyStatus = vehicleFileInfo.getPropertyStatus();
                    if (RiskUtils.isIgnoreAlarm(propertyStatus, vehicleFileInfo.getDeliveryStatus())){
                        continue;
                    }
                    //逻辑处理
                    processLongOrShortTermGpsData(vehicleFileInfo,vehicleGPSData,vehicleRiskStatus,riskAlarm);

                }catch (Exception e){
                    log.error("vehicleElectronicFenceWarningHandler:车辆超出电子围栏预警! vin:{}", vehicle.getVin(), e);
                }
            }
            pageNum++;
            /*queryRiskAlarmConfigRes = tableRiskAlarmConfigService.selectList(QueryRiskAlarmConfigReq.newBuilder().setAlarmType(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())
                    .setPageNum(pageNum).setPageSize(pageSize).build());*/
        }while (CollectionUtil.isNotEmpty(queryRiskAlarmConfigRes));
        log.info("vehicleElectronicFenceWarningHandler:结束执行任务!");
        return ReturnT.SUCCESS;
    }

    /**
     * 长短租，车辆超出电子围栏逻辑处理
     * @param vehicle
     * @param vehicleGPSData
     * @param vehicleRiskStatus
     */
    private void processLongOrShortTermGpsData(SearchVehicleFileList vehicle, VehicleGPSData vehicleGPSData, Integer vehicleRiskStatus,RiskAlarm riskAlarm) {
        log.info("{} 处理GPS上传时间的逻辑结果3："+ JSONUtil.toJsonStr(vehicle),vehicle.getVin());
        log.info("{} 处理GPS上传时间的逻辑结果4："+ JSONUtil.toJsonStr(vehicleGPSData),vehicle.getVin());
        long dayNum = DateUtil.daysBetween(new Date(),new Date(vehicleGPSData.getGpsDateTime()));
        //long minutesNum = DateUtil.minutesBetween(new Date(vehicleGPSData.getGpsDateTime()), new Date());
        log.info("{} dayNum："+ dayNum,vehicle.getVin());
        //log.info("{} minutesNum："+ minutesNum,vehicle.getVin());

        if (!vehicleGPSData.getCityName().equals(vehicleGPSData.getPenCityScope())){
            dayNum = Global.instance.redisUtil.incr(RealFields.VEHICLE_ELECTRON_KEY + vehicle.getVin());
            log.info("{} 车辆超电子围栏:{}天", vehicle.getVin(), dayNum);
        }else {
            // 如果最后一次定位在电子围栏内就不报警
            return;
        }

        if (null == riskAlarm) {
            //查询当前车辆存在的进行中的最高级别告警
            if (vehicleRiskStatus<VehicleRiskStatusEnum.RISK_VEHICLE.getCode()){
                subAssetsService.updateVehicleRiskStatusByVin(vehicle.getVin(),VehicleRiskStatusEnum.RISK_VEHICLE.getCode());
            }
            if ((vehicle.getProductLine() == LONG_TERM_CODE && dayNum >= DAYS_WARNING_3 && dayNum < DAYS_WARNING_7)
                    || (vehicle.getProductLine() == SHORT_TERM_CODE && dayNum >= 0 && dayNum < DAYS_WARNING_3 + DAYS_WARNING_7)) {//一级告警
                riskCommonService.addRiskAlarm(vehicle,vehicleGPSData,AlarmLevenEnum.ONE.getCode(),AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode(),VehicleRiskStatusEnum.RISK_VEHICLE.getCode());
                return;
            }else if ((vehicle.getProductLine() == LONG_TERM_CODE && dayNum >= DAYS_WARNING_7)
                    || (vehicle.getProductLine() == SHORT_TERM_CODE && dayNum >=  DAYS_WARNING_3 + DAYS_WARNING_7)) {//二级告警
                riskCommonService.addRiskAlarm(vehicle,vehicleGPSData,AlarmLevenEnum.TWO.getCode(),AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode(),VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode());
                return;
            }else {
                return;
            }
        }

        //暂停告警逻辑
        if(riskAlarm.getAlarmStatus() != AlarmStatusEnum.ALARM_STATUS.getCode() && null != vehicleGPSData.getPauseDeadlineTime() && vehicleGPSData.getPauseDeadlineTime().before(new Date())){
            isStopAlarm(riskAlarm,vehicle,vehicleRiskStatus,vehicleGPSData);
            return;
        }
        // 如果报警不是暂停，且告警状态是恢复，则不处理
        if (riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_RECOVER.getCode())){
            return;
        }
        // 如果车辆产品线是车管中心则恢复报警
        //恢复告警逻辑
        if (vehicleGPSData.getPenCityScope().contains(vehicleGPSData.getCityName()) || vehicle.getProductLine() == 1) {
            riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
            riskAlarm.setRecoverChannel(AlarmSystemEnum.WTXT.getCode());
            riskCommonService.updateRiskAlarm(false,riskAlarm,vehicleGPSData,vehicle.getProductLine());

            // 删除redis中的数据
            if (riskAlarm.getAlarmType().equals(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())){
                Global.instance.redisUtil.del(RealFields.VEHICLE_ELECTRON_KEY + vehicle.getVin());
            }
            //通知长租
            if (vehicle.getProductLine()==LONG_TERM_CODE || vehicle.getProductLine()==ParserForTypeEnum.VEHICLE_MANAGEMENT_CENTER.getCode()){
                subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
            }

            return;
        }
        //升级告警逻辑
        long upgradeDayNum = DateUtil.daysBetween(new Date(),riskAlarm.getAlarmTime());
        if ((vehicle.getProductLine() == LONG_TERM_CODE && riskAlarm != null && upgradeDayNum >= DAYS_WARNING_7) ||
                (vehicle.getProductLine() == SHORT_TERM_CODE && riskAlarm != null && upgradeDayNum >= DAYS_WARNING_7)) {
            riskCommonService.updateRiskAlarm(true,riskAlarm,vehicleGPSData,vehicle.getProductLine());
            Integer alarmLevel = riskAlarm.getAlarmLevel();
            // 查看有没有推过长租
            RiskPushRecord pushRecord = tableRiskPushRecordService.query(riskAlarm.getAlarmNo());
            if (pushRecord != null){
                return;
            }
            if (vehicle.getProductLine() == LONG_TERM_CODE && alarmLevel.equals(AlarmLevenEnum.TWO.getCode())){
                log.info("{} 是长租：",vehicle.getVin());

                // 如果产品线是长租，且有进行中的长租订单 则推送长租
                QueryOperateContractInfoByVinsRes queryOperateContractInfoByVinsRes = mdDataProxy.queryOperateContractInfoByVins(QueryOperateContractInfoByVinsReq.newBuilder().addVinList(vehicle.getVin()).build());
                if (queryOperateContractInfoByVinsRes == null || queryOperateContractInfoByVinsRes.getRetCode() != 0){
                    log.error("查询长租订单合同信息失败! vin:{}", vehicle.getVin());
                    return;
                }
                List<OperateContractInfo> operateContractInfoList = queryOperateContractInfoByVinsRes.getInfoList();
                if (CollectionUtil.isEmpty(operateContractInfoList)){
                    log.error("查询长租订单合同信息为空! vin:{}", vehicle.getVin());
                    return;
                }
                // 获取订单号
                OperateContractInfo operateContractInfo = operateContractInfoList.get(0);
                String orderNo = operateContractInfo.getOrderNo();
                log.info("电子围栏推送长租======>订单号：{}，车架号：{},报警等级：{}",orderNo,vehicle.getVin(),alarmLevel);
                RiskUtils.notifyRiskWorkOrderToLongRent(orderNo, vehicle.getPlateNo(), alarmLevel, riskAlarm.getAlarmNo(), riskAlarm.getAlarmType(), 1);
                // 记录推送标识，恢复报警需要判断是否已经推送过
                tableRiskPushRecordService.insert(riskAlarm.getAlarmNo());
                // 更新车辆报警所有等级
                subAssetsService.updateVehicleRiskStatusByVin(vehicle.getVin(), vehicleRiskStatus>alarmLevel+1?vehicleRiskStatus:alarmLevel+1);            }
        }
    }

    /**
     * 停止告警逻辑
     * @param riskAlarm
     * @param assetsVehicle
     * @param vehicleRiskStatus
     * @param vehicleGPSData
     * @return
     */
    private void isStopAlarm(RiskAlarm riskAlarm,SearchVehicleFileList assetsVehicle,Integer vehicleRiskStatus, VehicleGPSData vehicleGPSData) {
        RiskAlarm updateRiskAlarm = new RiskAlarm();
        updateRiskAlarm.setId(riskAlarm.getId());
        updateRiskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
        tableRiskAlarmService.update(updateRiskAlarm);
        saveLog(riskAlarm.getAlarmNo(), StrUtil.format("暂停后恢复风控报警【{}】", AlarmTypeEnum.getValueByCode(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())));
        Date now = new Date();
        Integer vehicleRiskStatusNew = 0;
        //判断是否超时
        long dayNum = DateUtil.daysBetween(new Date(),new Date(vehicleGPSData.getGpsDateTime()));
        long minutesNum = DateUtil.minutesBetween(new Date(vehicleGPSData.getGpsDateTime()), new Date());
        if ((assetsVehicle.getProductLine() == LONG_TERM_CODE &&  dayNum >= DAYS_WARNING_3 && dayNum < DAYS_WARNING_7)
                || (assetsVehicle.getProductLine() == SHORT_TERM_CODE && minutesNum >= MINUTE_WARNING_30 && dayNum < DAYS_WARNING_3 + DAYS_WARNING_7)) { //一级告警
            riskAlarm.setAlarmLevel(AlarmLevenEnum.ONE.getCode());
            if(vehicleRiskStatus < VehicleRiskStatusEnum.RISK_VEHICLE.getCode()){
                vehicleRiskStatusNew = VehicleRiskStatusEnum.RISK_VEHICLE.getCode();
            }
        }else if ((assetsVehicle.getProductLine() == LONG_TERM_CODE && dayNum >= DAYS_WARNING_7)
                || (assetsVehicle.getProductLine() == SHORT_TERM_CODE && dayNum >= DAYS_WARNING_3 + DAYS_WARNING_7)) { //二级告警
            riskAlarm.setAlarmLevel(AlarmLevenEnum.TWO.getCode());
            if(vehicleRiskStatus < VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode()){
                vehicleRiskStatusNew = VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode();
            }
        }else {
            return;
        }
        //创建新告警记录
        riskAlarm.setId(null);
        riskAlarm.setRecoveryDate(null);
        riskAlarm.setRecoverMethod(null);
        riskAlarm.setStopAlarmDay(null);
        riskAlarm.setPauseDeadlineTime(null);
        riskAlarm.setAlarmTime(now);
        riskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS.getCode());
        riskAlarm.setAlarmNo(outDescIdUtil.nextId("FKBJ"));
        if(null != vehicleGPSData){
            if(null != vehicleGPSData.getOldGpsDateTime()){
                riskAlarm.setLastLocationTime(new Date(vehicleGPSData.getOldGpsDateTime()));
            }
            riskAlarm.setLastLocation(vehicleGPSData.getLastAddress());
        }
        riskAlarm.setCreateTime(now);
        riskAlarm.setCreateOperName("定时任务");
        riskAlarm.setCreateOperAccount(StringUtils.EMPTY);
        riskAlarm.setRecoverChannel(0);
        tableRiskAlarmService.save(riskAlarm);
        if(vehicleRiskStatusNew > vehicleRiskStatus){
            subAssetsService.updateVehicleRiskStatusByVin(assetsVehicle.getVin(),vehicleRiskStatusNew);
        }
        saveLog(riskAlarm.getAlarmNo(), StrUtil.format("添加风控类型【{}】", AlarmTypeEnum.getValueByCode(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())));
    }

    /**
     * 记录日志
     * @param relationKey
     * @param operateContent
     */
    private void saveLog(String relationKey, String operateContent) {
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(OperateTypeEnum.OPERATE_TYPE.getCode());
        operateLog.setOperateContent(operateContent);
        operateLog.setCreateOperName("定时任务");
        operateLog.setCreateOperAccount("System");
        tableOperateLogService.save(operateLog);
    }
}
